#!/usr/bin/env python3
"""
Test final fixes for DermatoGemma Multi-Detection System v2.0 - Ollama Edition
"""

# Import warning suppression module FIRST
import suppress_warnings

import sys
from pathlib import Path
import numpy as np

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_abcde_missing_methods():
    """Test that all ABCDE missing methods are now implemented"""
    print("🧪 Testing ABCDE missing methods...")
    
    try:
        from core.abcde_analyzer import AdvancedABCDEAnalyzer
        
        # Create analyzer instance
        analyzer = AdvancedABCDEAnalyzer()
        
        # Check if all required methods exist
        required_methods = [
            '_analyze_pigmentation_patterns',
            '_calculate_analysis_confidence'
        ]
        
        print("   - Checking method existence:")
        for method_name in required_methods:
            if hasattr(analyzer, method_name):
                print(f"     ✅ {method_name}: EXISTS")
            else:
                print(f"     ❌ {method_name}: MISSING")
                return False
        
        # Test pigmentation patterns method
        test_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        pigmentation_score = analyzer._analyze_pigmentation_patterns(test_image)
        print(f"   - Pigmentation analysis: {pigmentation_score:.3f}")
        
        # Test confidence calculation with proper arguments
        mock_abcde_results = {
            'asymmetry': {'score': 0.3, 'confidence': 0.8},
            'border': {'score': 0.5, 'confidence': 0.7},
            'color': {'overall_score': 0.4, 'confidence': 0.9},
            'diameter': {'score': 0.6, 'confidence': 0.8},
            'evolution': {'score': 0.2, 'confidence': 0.6}
        }
        
        confidence_result = analyzer._calculate_analysis_confidence(mock_abcde_results)
        print(f"   - Confidence calculation: {confidence_result.get('overall_confidence', 0):.3f}")
        
        print("✅ ABCDE missing methods test completed successfully!")
        print("   - All required methods are now implemented")
        print("   - Methods execute without errors")
        print("   - Proper argument handling")
        
        return True
        
    except Exception as e:
        print(f"❌ ABCDE missing methods test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_analysis_modes():
    """Test UI analysis mode selection"""
    print("🧪 Testing UI analysis mode selection...")
    
    try:
        from ui_modern import ModernDermatoGemmaUI
        
        # Create GUI instance
        app = ModernDermatoGemmaUI()
        
        # Check if analysis mode controls exist
        if hasattr(app, 'analysis_mode'):
            print("   ✅ Analysis mode variable exists")
            
            # Test default mode
            default_mode = app.analysis_mode.get()
            print(f"   ✅ Default mode: {default_mode}")
            
            # Test mode change
            app.analysis_mode.set("single")
            app.on_analysis_mode_change()
            print("   ✅ Single mode set successfully")
            
            app.analysis_mode.set("multi")
            app.on_analysis_mode_change()
            print("   ✅ Multi mode set successfully")
            
        else:
            print("   ❌ Analysis mode variable missing")
            return False
        
        # Check if condition selection exists
        if hasattr(app, 'selected_condition'):
            print("   ✅ Condition selection exists")
            
            # Test condition options
            if hasattr(app, 'condition_options'):
                print(f"   ✅ Condition options: {len(app.condition_options)} conditions")
                print(f"     - First condition: {app.condition_options[0]}")
                print(f"     - Last condition: {app.condition_options[-1]}")
            else:
                print("   ❌ Condition options missing")
                return False
                
        else:
            print("   ❌ Condition selection missing")
            return False
        
        print("✅ UI analysis modes test completed successfully!")
        print("   - Analysis mode selection implemented")
        print("   - Condition dropdown available")
        print("   - Mode change handling works")
        
        # Don't start mainloop for automated testing
        app.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ UI analysis modes test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_single_condition_analysis():
    """Test single condition analysis functionality"""
    print("🧪 Testing single condition analysis...")
    
    try:
        from core.gemma_derma_handler import GemmaDermatologyHandler
        
        # Create handler instance
        handler = GemmaDermatologyHandler()
        
        # Check if single condition method exists
        if hasattr(handler, 'analyze_single_condition'):
            print("   ✅ analyze_single_condition method exists")
            
            # Test method signature (without actually calling it)
            import inspect
            sig = inspect.signature(handler.analyze_single_condition)
            params = list(sig.parameters.keys())
            print(f"   ✅ Method parameters: {params}")
            
            # Check required parameters
            required_params = ['lesion_image', 'target_condition']
            for param in required_params:
                if param in params:
                    print(f"     ✅ {param}: present")
                else:
                    print(f"     ❌ {param}: missing")
                    return False
            
        else:
            print("   ❌ analyze_single_condition method missing")
            return False
        
        print("✅ Single condition analysis test completed successfully!")
        print("   - Method implemented with correct signature")
        print("   - All required parameters present")
        print("   - Ready for single-condition analysis")
        
        return True
        
    except Exception as e:
        print(f"❌ Single condition analysis test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_analysis_options():
    """Test analysis options configuration"""
    print("🧪 Testing analysis options configuration...")
    
    try:
        from ui_modern import ModernDermatoGemmaUI
        
        # Create GUI instance
        app = ModernDermatoGemmaUI()
        
        # Create mock image
        test_image = np.ones((200, 200, 3), dtype=np.uint8) * 128
        app.current_image = test_image
        
        # Test multi-condition options
        app.analysis_mode.set("multi")
        app.selected_condition.set("Melanoma")
        
        # Simulate analysis options creation
        analysis_mode = app.analysis_mode.get()
        selected_condition = app.selected_condition.get() if analysis_mode == "single" else None
        
        multi_options = {
            'enable_abcde_analysis': True,
            'enable_ai_analysis': True,
            'enable_multi_condition': analysis_mode == "multi",
            'enable_single_condition': analysis_mode == "single",
            'target_condition': selected_condition,
            'force_llm_analysis': True,
            'llm_detailed_analysis': True
        }
        
        print("   - Multi-condition options:")
        for key, value in multi_options.items():
            print(f"     ✅ {key}: {value}")
        
        # Test single-condition options
        app.analysis_mode.set("single")
        app.selected_condition.set("Basal Cell Carcinoma")
        
        analysis_mode = app.analysis_mode.get()
        selected_condition = app.selected_condition.get() if analysis_mode == "single" else None
        
        single_options = {
            'enable_abcde_analysis': True,
            'enable_ai_analysis': True,
            'enable_multi_condition': analysis_mode == "multi",
            'enable_single_condition': analysis_mode == "single",
            'target_condition': selected_condition,
            'force_llm_analysis': True,
            'llm_detailed_analysis': True
        }
        
        print("   - Single-condition options:")
        for key, value in single_options.items():
            print(f"     ✅ {key}: {value}")
        
        # Validate options
        if single_options['enable_single_condition'] and single_options['target_condition']:
            print("   ✅ Single-condition mode properly configured")
        else:
            print("   ❌ Single-condition mode configuration failed")
            return False
        
        print("✅ Analysis options test completed successfully!")
        print("   - Multi-condition options work correctly")
        print("   - Single-condition options work correctly")
        print("   - Target condition properly set")
        
        # Don't start mainloop for automated testing
        app.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Analysis options test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run final fixes tests"""
    print("🏥 DermatoGemma Multi-Detection System v2.0 - Ollama Edition")
    print("🧪 Testing final fixes and new features...\n")
    
    tests = [
        ("ABCDE Missing Methods", test_abcde_missing_methods),
        ("UI Analysis Modes", test_ui_analysis_modes),
        ("Single Condition Analysis", test_single_condition_analysis),
        ("Analysis Options", test_analysis_options),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
        print()
    
    print(f"{'='*70}")
    print(f"🧪 Final Fixes Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All final fixes tests passed!")
        print("\n✨ Issues Fixed:")
        print("   ✅ _analyze_pigmentation_patterns implemented")
        print("   ✅ _calculate_analysis_confidence arguments fixed")
        print("   ✅ Analysis mode selection added (Multi/Single)")
        print("   ✅ Condition dropdown with 14 diseases")
        print("   ✅ Single-condition analysis method implemented")
        print("   ✅ Analysis options properly configured")
        print("\n🎯 New Features Added:")
        print("   ✅ Multi-condition analysis (all 14 diseases)")
        print("   ✅ Single-condition analysis (choose specific disease)")
        print("   ✅ Dynamic UI controls for mode selection")
        print("   ✅ LLM analysis for both modes")
        print("\n💡 The system now supports both analysis modes!")
        print("   Run 'python main.py' to test the full interface.")
        print("   Choose between Multi-condition or Single-condition analysis.")
    else:
        print("⚠️ Some final fixes tests failed")
        print("Check the specific test failures above")
    
    print(f"{'='*70}")

if __name__ == "__main__":
    main()
