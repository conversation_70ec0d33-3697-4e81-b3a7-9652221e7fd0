#!/usr/bin/env python3
"""
REAL Gemma 3n E4B Test - NO FALLBACKS ALLOWED
Tests ONLY the REAL model functionality via Ollama

This script validates:
- REAL Gemma 3n E4B model availability and functionality
- REAL multimodal image analysis capabilities
- REAL visual data extraction and interpretation
- NO SIMULATIONS OR FALLBACKS - REAL RESULTS ONLY
"""

import sys
import os
import logging
import time
import numpy as np
from PIL import Image
from pathlib import Path
import requests

# Add project root to path
sys.path.append(str(Path(__file__).parent))

# Import core modules
from core.gemma_derma_handler import GemmaDermatologyHandlerV2

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RealGemma3nE4BValidator:
    """REAL Gemma 3n E4B validator - NO FALLBACKS"""
    
    def __init__(self):
        self.ollama_url = "http://localhost:11434"
        self.model_name = "gemma3n:e4b"
        
    def validate_real_system(self):
        """Validate REAL system functionality - NO FALLBACKS ALLOWED"""
        logger.info("🔥 VALIDATING REAL GEMMA 3N E4B SYSTEM - NO FALLBACKS ALLOWED")
        logger.info("=" * 80)
        
        try:
            # Step 1: Verify Ollama is running
            self._verify_ollama_service()
            
            # Step 2: Verify REAL Gemma 3n E4B model is available
            self._verify_real_model()
            
            # Step 3: Test REAL model functionality
            self._test_real_model_functionality()
            
            # Step 4: Test REAL multimodal capabilities
            self._test_real_multimodal_capabilities()
            
            # Step 5: Initialize REAL handler (NO FALLBACKS)
            self._test_real_handler_initialization()
            
            # Step 6: Test REAL dermatological analysis
            self._test_real_dermatological_analysis()
            
            logger.info("🎉 ALL REAL SYSTEM VALIDATIONS PASSED!")
            logger.info("✅ REAL Gemma 3n E4B system is fully functional")
            
        except Exception as e:
            logger.error(f"❌ CRITICAL FAILURE: {e}")
            logger.error("🚫 REAL system validation FAILED - NO FALLBACKS AVAILABLE")
            raise
    
    def _verify_ollama_service(self):
        """Verify Ollama service is running"""
        logger.info("🔍 Verifying Ollama service...")
        
        try:
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=3600)
            if response.status_code != 200:
                raise RuntimeError("❌ Ollama service is not responding!")
            
            logger.info("✅ Ollama service is running")
            
        except requests.exceptions.ConnectionError:
            raise RuntimeError(
                "❌ CRITICAL: Cannot connect to Ollama!\n"
                "Please ensure Ollama is running:\n"
                "1. Install Ollama: https://ollama.ai/\n"
                "2. Start service: ollama serve\n"
                "NO FALLBACKS ALLOWED!"
            )
    
    def _verify_real_model(self):
        """Verify REAL Gemma 3n E4B model is available"""
        logger.info("🔍 Verifying REAL Gemma 3n E4B model...")
        
        try:
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=3600)
            models_data = response.json()
            available_models = [model['name'] for model in models_data.get('models', [])]
            
            if self.model_name not in available_models:
                raise RuntimeError(
                    f"❌ CRITICAL: {self.model_name} model is NOT available!\n"
                    f"Available models: {available_models}\n"
                    f"Please run: ollama pull {self.model_name}\n"
                    "NO FALLBACKS ALLOWED!"
                )
            
            logger.info(f"✅ REAL {self.model_name} model is available")
            
        except Exception as e:
            raise RuntimeError(f"❌ CRITICAL: Model verification failed: {e}")
    
    def _test_real_model_functionality(self):
        """Test REAL model functionality"""
        logger.info("🧪 Testing REAL model functionality...")
        
        try:
            test_response = requests.post(
                f"{self.ollama_url}/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": "Test: What are your multimodal capabilities for medical image analysis?",
                    "stream": False,
                    "options": {"num_predict": 100}
                },
                timeout=3600
            )
            
            if test_response.status_code != 200:
                raise RuntimeError(f"❌ Model test failed with status: {test_response.status_code}")
            
            response_data = test_response.json()
            if not response_data.get('response'):
                raise RuntimeError("❌ Model returned empty response!")
            
            logger.info("✅ REAL model functionality confirmed")
            logger.info(f"📝 Model response preview: {response_data['response'][:150]}...")
            
        except Exception as e:
            raise RuntimeError(f"❌ CRITICAL: Model functionality test failed: {e}")
    
    def _test_real_multimodal_capabilities(self):
        """Test REAL multimodal capabilities with actual image"""
        logger.info("🖼️ Testing REAL multimodal capabilities...")
        
        try:
            # Create a test medical image
            test_image = self._create_test_medical_image()
            
            # Test multimodal request
            test_response = requests.post(
                f"{self.ollama_url}/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": "Analyze this medical image and describe what you see.",
                    "images": [test_image],
                    "stream": False,
                    "options": {"num_predict": 200}
                },
                timeout=3600
            )
            
            if test_response.status_code != 200:
                raise RuntimeError(f"❌ Multimodal test failed with status: {test_response.status_code}")
            
            response_data = test_response.json()
            if not response_data.get('response'):
                raise RuntimeError("❌ Multimodal analysis returned empty response!")
            
            logger.info("✅ REAL multimodal capabilities confirmed")
            logger.info(f"🔬 Multimodal analysis preview: {response_data['response'][:150]}...")
            
        except Exception as e:
            raise RuntimeError(f"❌ CRITICAL: Multimodal test failed: {e}")
    
    def _create_test_medical_image(self) -> str:
        """Create a test medical image for validation"""
        try:
            # Create a simple test image (skin-like colors)
            img_array = np.random.randint(180, 220, (512, 512, 3), dtype=np.uint8)
            
            # Add some variation to simulate skin texture
            for i in range(100, 400):
                for j in range(100, 400):
                    img_array[i, j] = [200, 150, 120]  # Skin-like color
            
            # Add a darker spot to simulate a lesion
            for i in range(200, 300):
                for j in range(200, 300):
                    img_array[i, j] = [100, 80, 60]  # Darker lesion-like color
            
            # Convert to PIL and then to base64
            image = Image.fromarray(img_array)
            
            import io
            import base64
            buffer = io.BytesIO()
            image.save(buffer, format='JPEG', quality=95)
            return base64.b64encode(buffer.getvalue()).decode('utf-8')
            
        except Exception as e:
            raise RuntimeError(f"❌ Test image creation failed: {e}")
    
    def _test_real_handler_initialization(self):
        """Test REAL handler initialization - NO FALLBACKS"""
        logger.info("🔧 Testing REAL handler initialization...")
        
        try:
            # Initialize handler with NO lazy loading to force real verification
            handler = GemmaDermatologyHandlerV2(lazy_load=False)
            
            if not handler.is_ready():
                raise RuntimeError("❌ Handler is not ready after initialization!")
            
            logger.info("✅ REAL handler initialized successfully")
            
        except Exception as e:
            raise RuntimeError(f"❌ CRITICAL: Handler initialization failed: {e}")
    
    def _test_real_dermatological_analysis(self):
        """Test REAL dermatological analysis with actual processing"""
        logger.info("🔬 Testing REAL dermatological analysis...")
        
        try:
            # Initialize handler
            handler = GemmaDermatologyHandlerV2(lazy_load=False)
            
            # Create test image
            test_image = np.random.randint(180, 220, (512, 512, 3), dtype=np.uint8)
            
            # Add lesion-like features
            test_image[200:300, 200:300] = [100, 80, 60]
            
            # Mock ABCDE results
            abcde_results = {
                'asymmetry': {'score': 0.4, 'is_concerning': False},
                'border': {'score': 0.3, 'is_concerning': False},
                'color': {'score': 0.5, 'is_concerning': True},
                'diameter': {'max_diameter_mm': 5.2, 'is_concerning': False},
                'evolution': {'score': 0.2, 'is_concerning': False},
                'overall_score': 0.34,
                'success': True
            }
            
            # Run REAL analysis
            logger.info("🚀 Running REAL multimodal dermatological analysis...")
            results = handler.analyze_multi_condition(
                lesion_image=test_image,
                abcde_results=abcde_results,
                lesion_metadata={'source': 'test'},
                patient_context={'age': 45, 'gender': 'female'}
            )
            
            # Validate results
            if not results.get('success', False):
                raise RuntimeError(f"❌ Analysis failed: {results.get('error', 'Unknown error')}")
            
            if 'condition_analyses' not in results:
                raise RuntimeError("❌ Missing condition analyses in results!")
            
            logger.info("✅ REAL dermatological analysis completed successfully")
            logger.info(f"📊 Analysis results: {len(results['condition_analyses'])} conditions analyzed")
            
            # Log some key results
            for condition_id, analysis in results['condition_analyses'].items():
                probability = analysis.get('probability', 0)
                logger.info(f"   {condition_id}: {probability:.3f} probability")
            
        except Exception as e:
            raise RuntimeError(f"❌ CRITICAL: Dermatological analysis failed: {e}")

def main():
    """Main validation execution"""
    print("🔥 REAL GEMMA 3N E4B VALIDATION - NO FALLBACKS ALLOWED")
    print("=" * 80)
    print("This test validates ONLY the REAL model functionality.")
    print("NO simulations, fallbacks, or mock responses allowed!")
    print()
    
    validator = RealGemma3nE4BValidator()
    
    try:
        validator.validate_real_system()
        print("\n🎉 SUCCESS: REAL Gemma 3n E4B system is fully functional!")
        print("✅ Ready for production dermatological analysis")
        
    except Exception as e:
        print(f"\n❌ VALIDATION FAILED: {e}")
        print("🚫 System is NOT ready for production use")
        print("Please fix the issues above before proceeding")
        sys.exit(1)

if __name__ == "__main__":
    main()
