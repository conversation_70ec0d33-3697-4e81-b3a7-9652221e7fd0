#!/usr/bin/env python3
"""
Setup Script for REAL Gemma 3n E4B System
Installs and configures Ollama with the REAL Gemma 3n E4B model

NO FALLBACKS - REAL MODEL ONLY
"""

import subprocess
import sys
import time
import requests
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RealGemma3nE4BSetup:
    """Setup REAL Gemma 3n E4B system - NO FALLBACKS"""
    
    def __init__(self):
        self.ollama_url = "http://localhost:11434"
        self.model_name = "gemma3n:e4b"
        
    def setup_real_system(self):
        """Setup REAL Gemma 3n E4B system"""
        logger.info("🚀 SETTING UP REAL GEMMA 3N E4B SYSTEM")
        logger.info("=" * 60)
        
        try:
            # Step 1: Check if Ollama is installed
            self._check_ollama_installation()
            
            # Step 2: Start Ollama service
            self._start_ollama_service()
            
            # Step 3: Pull REAL Gemma 3n E4B model
            self._pull_real_model()
            
            # Step 4: Verify model functionality
            self._verify_model_functionality()
            
            # Step 5: Test multimodal capabilities
            self._test_multimodal_capabilities()
            
            logger.info("🎉 REAL GEMMA 3N E4B SYSTEM SETUP COMPLETE!")
            logger.info("✅ System is ready for production use")
            
        except Exception as e:
            logger.error(f"❌ SETUP FAILED: {e}")
            raise
    
    def _check_ollama_installation(self):
        """Check if Ollama is installed"""
        logger.info("🔍 Checking Ollama installation...")
        
        try:
            result = subprocess.run(['ollama', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                logger.info(f"✅ Ollama is installed: {result.stdout.strip()}")
            else:
                raise RuntimeError("Ollama not found")
                
        except (subprocess.TimeoutExpired, FileNotFoundError, RuntimeError):
            logger.error("❌ Ollama is not installed!")
            logger.info("📥 Installing Ollama...")
            self._install_ollama()
    
    def _install_ollama(self):
        """Install Ollama"""
        try:
            if sys.platform.startswith('win'):
                logger.info("🪟 Windows detected - Please install Ollama manually:")
                logger.info("1. Download from: https://ollama.ai/download/windows")
                logger.info("2. Run the installer")
                logger.info("3. Restart this script")
                raise RuntimeError("Please install Ollama manually on Windows")
            
            elif sys.platform.startswith('darwin'):
                logger.info("🍎 macOS detected - Installing Ollama...")
                subprocess.run(['curl', '-fsSL', 'https://ollama.ai/install.sh'], 
                             stdout=subprocess.PIPE, check=True)
                
            elif sys.platform.startswith('linux'):
                logger.info("🐧 Linux detected - Installing Ollama...")
                subprocess.run(['curl', '-fsSL', 'https://ollama.ai/install.sh', '|', 'sh'], 
                             shell=True, check=True)
            
            else:
                raise RuntimeError(f"Unsupported platform: {sys.platform}")
                
            logger.info("✅ Ollama installation completed")
            
        except Exception as e:
            raise RuntimeError(f"Ollama installation failed: {e}")
    
    def _start_ollama_service(self):
        """Start Ollama service"""
        logger.info("🔄 Starting Ollama service...")
        
        try:
            # Check if already running
            try:
                response = requests.get(f"{self.ollama_url}/api/tags", timeout=5)
                if response.status_code == 200:
                    logger.info("✅ Ollama service is already running")
                    return
            except:
                pass
            
            # Start service
            if sys.platform.startswith('win'):
                # On Windows, Ollama usually starts automatically
                logger.info("🪟 Starting Ollama service on Windows...")
                subprocess.Popen(['ollama', 'serve'], 
                               creationflags=subprocess.CREATE_NEW_CONSOLE)
            else:
                # On Unix systems
                logger.info("🐧 Starting Ollama service...")
                subprocess.Popen(['ollama', 'serve'])
            
            # Wait for service to start
            logger.info("⏳ Waiting for Ollama service to start...")
            for i in range(30):  # Wait up to 30 seconds
                try:
                    response = requests.get(f"{self.ollama_url}/api/tags", timeout=2)
                    if response.status_code == 200:
                        logger.info("✅ Ollama service started successfully")
                        return
                except:
                    pass
                time.sleep(1)
            
            raise RuntimeError("Ollama service failed to start within 30 seconds")
            
        except Exception as e:
            raise RuntimeError(f"Failed to start Ollama service: {e}")
    
    def _pull_real_model(self):
        """Pull REAL Gemma 3n E4B model"""
        logger.info(f"📥 Pulling REAL {self.model_name} model...")
        logger.info("⚠️  This may take several minutes depending on your internet connection")
        
        try:
            # Check if model already exists
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=10)
            if response.status_code == 200:
                models_data = response.json()
                available_models = [model['name'] for model in models_data.get('models', [])]
                
                if self.model_name in available_models:
                    logger.info(f"✅ {self.model_name} model already available")
                    return
            
            # Pull the model
            logger.info(f"🔄 Downloading {self.model_name} model...")
            result = subprocess.run(['ollama', 'pull', self.model_name], 
                                  capture_output=True, text=True, timeout=1800)  # 30 minutes timeout
            
            if result.returncode != 0:
                raise RuntimeError(f"Model pull failed: {result.stderr}")
            
            logger.info(f"✅ {self.model_name} model downloaded successfully")
            
        except subprocess.TimeoutExpired:
            raise RuntimeError("Model download timed out (30 minutes)")
        except Exception as e:
            raise RuntimeError(f"Failed to pull model: {e}")
    
    def _verify_model_functionality(self):
        """Verify REAL model functionality"""
        logger.info("🧪 Verifying REAL model functionality...")
        
        try:
            test_response = requests.post(
                f"{self.ollama_url}/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": "Hello! Can you analyze medical images?",
                    "stream": False,
                    "options": {"num_predict": 50}
                },
                timeout=3600
            )
            
            if test_response.status_code != 200:
                raise RuntimeError(f"Model test failed: {test_response.status_code}")
            
            response_data = test_response.json()
            if not response_data.get('response'):
                raise RuntimeError("Model returned empty response")
            
            logger.info("✅ REAL model functionality verified")
            logger.info(f"📝 Model response: {response_data['response'][:100]}...")
            
        except Exception as e:
            raise RuntimeError(f"Model verification failed: {e}")
    
    def _test_multimodal_capabilities(self):
        """Test REAL multimodal capabilities"""
        logger.info("🖼️ Testing REAL multimodal capabilities...")
        
        try:
            # Create a simple test image
            import numpy as np
            from PIL import Image
            import io
            import base64
            
            # Create test image
            img_array = np.random.randint(100, 200, (256, 256, 3), dtype=np.uint8)
            image = Image.fromarray(img_array)
            
            # Convert to base64
            buffer = io.BytesIO()
            image.save(buffer, format='JPEG')
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            # Test multimodal request
            test_response = requests.post(
                f"{self.ollama_url}/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": "Describe this image briefly.",
                    "images": [image_base64],
                    "stream": False,
                    "options": {"num_predict": 50}
                },
                timeout=3600
            )
            
            if test_response.status_code != 200:
                raise RuntimeError(f"Multimodal test failed: {test_response.status_code}")
            
            response_data = test_response.json()
            if not response_data.get('response'):
                raise RuntimeError("Multimodal test returned empty response")
            
            logger.info("✅ REAL multimodal capabilities verified")
            logger.info(f"🔬 Multimodal response: {response_data['response'][:100]}...")
            
        except Exception as e:
            raise RuntimeError(f"Multimodal test failed: {e}")
    
    def show_system_info(self):
        """Show system information"""
        logger.info("📊 REAL GEMMA 3N E4B SYSTEM INFORMATION")
        logger.info("=" * 50)
        
        try:
            # Ollama version
            result = subprocess.run(['ollama', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                logger.info(f"Ollama Version: {result.stdout.strip()}")
            
            # Available models
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=10)
            if response.status_code == 200:
                models_data = response.json()
                models = [model['name'] for model in models_data.get('models', [])]
                logger.info(f"Available Models: {models}")
                
                # Model details
                for model in models_data.get('models', []):
                    if model['name'] == self.model_name:
                        size_gb = model.get('size', 0) / (1024**3)
                        logger.info(f"{self.model_name} Size: {size_gb:.2f} GB")
                        logger.info(f"{self.model_name} Modified: {model.get('modified_at', 'Unknown')}")
            
            logger.info("✅ System ready for REAL dermatological analysis")
            
        except Exception as e:
            logger.warning(f"Could not retrieve system info: {e}")

def main():
    """Main setup execution"""
    print("🚀 REAL GEMMA 3N E4B SETUP")
    print("=" * 50)
    print("Setting up REAL Gemma 3n E4B model for dermatological analysis")
    print("NO FALLBACKS - REAL MODEL ONLY")
    print()
    
    setup = RealGemma3nE4BSetup()
    
    try:
        setup.setup_real_system()
        print("\n🎉 SUCCESS: REAL Gemma 3n E4B system is ready!")
        print("✅ You can now run the dermatological analysis")
        print()
        
        setup.show_system_info()
        
        print("\n🔬 Next steps:")
        print("1. Run: python test_real_gemma3n_e4b.py")
        print("2. Run: python main.py")
        
    except Exception as e:
        print(f"\n❌ SETUP FAILED: {e}")
        print("Please fix the issues above and try again")
        sys.exit(1)

if __name__ == "__main__":
    main()
