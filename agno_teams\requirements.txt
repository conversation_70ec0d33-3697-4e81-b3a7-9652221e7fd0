# DermatoGemma Multi-Agent System Requirements
# Agno Teams and dependencies for autonomous multi-agent dermatology analysis

# Core Agno Teams framework
agno>=1.7.6

# AI/ML Models and APIs
openai>=1.0.0
ollama>=0.1.0

# Vector Database and Embeddings
lancedb>=0.3.0
sentence-transformers>=2.2.0

# Memory and Storage
sqlalchemy>=2.0.0

# Image Processing
Pillow>=10.0.0
opencv-python>=4.8.0
numpy>=1.24.0
scikit-image>=0.21.0

# Data Processing
pandas>=2.0.0
pydantic>=2.0.0

# Web and Knowledge Base
requests>=2.31.0
beautifulsoup4>=4.12.0
newspaper4k>=0.9.0
lxml>=4.9.0
lxml_html_clean>=0.1.0

# Search Tools
duckduckgo-search>=3.9.0

# Async Support
aiohttp>=3.8.0

# Logging and Monitoring
structlog>=23.0.0
rich>=13.0.0

# Configuration and Environment
python-dotenv>=1.0.0
pyyaml>=6.0.0

# Testing (optional)
pytest>=7.4.0
pytest-asyncio>=0.21.0

# Development Tools (optional)
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0

# Medical/Scientific Libraries
scipy>=1.11.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Additional utilities
tqdm>=4.66.0
click>=8.1.0
typing-extensions>=4.7.0
