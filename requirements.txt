# 🏥 DermatoGemma Multi-Detection System v2.0 - Ollama Edition
# Simplified Requirements for Ollama Integration

# Core Scientific Computing
numpy>=1.21.0
scipy>=1.7.0
pandas>=1.3.0

# Computer Vision and Image Processing
opencv-python>=4.5.0
Pillow>=8.3.0
scikit-image>=0.18.0

# Machine Learning (Basic)
scikit-learn>=1.0.0

# GUI Framework - Modern UI/UX
customtkinter>=5.2.0
tqdm>=4.65.0

# HTTP Requests for Ollama API
requests>=2.31.0

# Medical Image Processing (Basic)
pydicom>=2.2.0

# Progress Bars and UI Enhancements
tqdm>=4.62.0

# Configuration Management
pyyaml>=5.4.0

# Logging and Monitoring
colorlog>=6.4.0

# Performance Optimization
joblib>=1.0.0

# Database Support (for patient data management)
sqlalchemy>=1.4.0

# Cryptography for Data Security
cryptography>=3.4.0

# Date and Time Utilities
python-dateutil>=2.8.0

# Memory Profiling and Optimization
psutil>=5.8.0

# Installation Notes:
# 1. Install Python 3.8 or higher
# 2. Install Ollama: https://ollama.ai/
# 3. Start Ollama: ollama serve
# 4. Pull Gemma model: ollama pull gemma3n:e2b
# 5. Create virtual environment: python -m venv dermatogamma_env
# 6. Activate environment: source dermatogamma_env/bin/activate (Linux/Mac) or dermatogamma_env\Scripts\activate (Windows)
# 7. Install requirements: pip install -r requirements_ollama.txt

# Ollama Setup:
# - Ollama must be running on localhost:11434
# - Model gemma3n:e2b must be available
# - No GPU required (Ollama handles model optimization)
# - Significantly reduced memory requirements compared to direct model loading

# Medical Compliance Notes:
# - This software is for research and screening purposes only
# - Not intended for primary diagnosis without physician oversight
# - Complies with HIPAA privacy requirements through local processing
# - All AI processing happens locally via Ollama
