#!/usr/bin/env python3
"""
DermatoGemma Multi-Detection System v2.0 - Gemma 3n E4B Edition
Modern Multimodal UI/UX - Revolutionary Visual Interface with AI-Powered Image Analysis

Enhanced with Gemma 3n E4B multimodal capabilities for advanced visual data extraction,
interpretation, and summarization. Features AI-powered image analysis, real-time visual
feature extraction, and comprehensive dermatological assessment.

Following RetinoblastoGemma-App design patterns with advanced visual elements,
modern styling, and professional medical interface design.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import customtkinter as ctk
from PIL import Image, ImageTk, ImageDraw, ImageFilter
import numpy as np
import cv2
from pathlib import Path
import threading
import time
from datetime import datetime
import json
import logging
from typing import Dict, List, Optional, Any

# Apply warning suppressions
try:
    from fix_warnings import apply_runtime_fixes
    apply_runtime_fixes()
except ImportError:
    import warnings
    import os
    warnings.filterwarnings("ignore", category=DeprecationWarning, module="paramiko")
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

# Configure CustomTkinter
ctk.set_appearance_mode("light")  # Professional medical appearance
ctk.set_default_color_theme("blue")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModernDermatoGemmaUI:
    """
    Modern Multimodal UI/UX for DermatoGemma Multi-Detection System with Gemma 3n E4B
    Revolutionary visual interface with AI-powered image analysis capabilities

    Features:
    - Advanced multimodal image processing and analysis
    - Real-time visual data extraction and interpretation
    - AI-powered dermatological assessment using Gemma 3n E4B
    - Professional medical interface with enhanced visual feedback
    - Comprehensive reporting with visual insights
    """
    
    def __init__(self):
        # Initialize main window
        self.root = ctk.CTk()
        self.root.title("DermatoGemma Multi-Detection System v2.0 - Gemma 3n E4B Multimodal Edition")
        self.root.geometry("1500x950")
        self.root.minsize(1300, 850)
        
        # Color scheme following medical professional standards
        self.colors = {
            'primary': '#2E86AB',      # Medical blue
            'secondary': '#A23B72',    # Accent purple
            'success': '#28A745',      # Success green
            'warning': '#FFC107',      # Warning yellow
            'danger': '#C73E1D',       # Alert red
            'error': '#C73E1D',        # Error red (alias for danger)
            'background': '#F8F9FA',   # Clean white
            'surface': '#FFFFFF',      # Pure white
            'text_primary': '#212529', # Dark text
            'text_secondary': '#6C757D', # Gray text
            'border': '#DEE2E6'        # Light border
        }
        
        # Initialize variables
        self.current_image = None
        self.analysis_results = None
        self.processing = False
        self.stop_requested = False
        
        # Setup UI components
        self.setup_styles()
        self.create_header()
        self.create_main_layout()
        self.create_sidebar()
        self.create_analysis_panel()
        self.create_results_panel()
        self.create_footer()
        
        # Initialize analysis engine
        self.init_analysis_engine()
        
        logger.info("🎨 Modern DermatoGemma UI initialized")
    
    def setup_styles(self):
        """Setup modern styling"""
        # Configure ttk styles for professional appearance
        style = ttk.Style()
        style.theme_use('clam')
        
        # Custom button styles
        style.configure(
            "Primary.TButton",
            background=self.colors['primary'],
            foreground='white',
            borderwidth=0,
            focuscolor='none',
            padding=(20, 10)
        )
        
        style.configure(
            "Success.TButton",
            background=self.colors['success'],
            foreground='white',
            borderwidth=0,
            focuscolor='none',
            padding=(15, 8)
        )
        
        # Progress bar style
        style.configure(
            "Medical.Horizontal.TProgressbar",
            background=self.colors['primary'],
            troughcolor=self.colors['border'],
            borderwidth=0,
            lightcolor=self.colors['primary'],
            darkcolor=self.colors['primary']
        )
    
    def create_header(self):
        """Create professional header with branding"""
        header_frame = ctk.CTkFrame(
            self.root,
            height=80,
            fg_color=self.colors['primary'],
            corner_radius=0
        )
        header_frame.pack(fill="x", padx=0, pady=0)
        header_frame.pack_propagate(False)
        
        # Logo and title
        title_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        title_frame.pack(side="left", padx=20, pady=15)
        
        # Main title
        title_label = ctk.CTkLabel(
            title_frame,
            text="🏥 DermatoGemma Multi-Detection System",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color="white"
        )
        title_label.pack(side="left")

        # Enhanced version badge with multimodal indicator
        version_label = ctk.CTkLabel(
            title_frame,
            text="v2.0 Gemma 3n E4B",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color="white",
            fg_color=self.colors['secondary'],
            corner_radius=15,
            width=140,
            height=25
        )
        version_label.pack(side="left", padx=(15, 0))

        # Multimodal AI indicator
        ai_badge = ctk.CTkLabel(
            title_frame,
            text="🤖 AI Multimodal",
            font=ctk.CTkFont(size=10),
            text_color="white",
            fg_color=self.colors['success'],
            corner_radius=12,
            width=100,
            height=20
        )
        ai_badge.pack(side="left", padx=(10, 0))

        # Action buttons frame (center)
        action_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        action_frame.pack(side="left", expand=True, padx=20, pady=15)

        # Clear screens button
        self.clear_btn = ctk.CTkButton(
            action_frame,
            text="🧹 Clear Screens",
            command=self.clear_all_screens,
            width=120,
            height=35,
            font=ctk.CTkFont(size=11, weight="bold"),
            fg_color=self.colors['warning'],
            hover_color=self.colors['secondary'],
            text_color="black"
        )
        self.clear_btn.pack(side="left", padx=(0, 10))

        # Stop analysis button
        self.stop_btn = ctk.CTkButton(
            action_frame,
            text="⏹️ Stop Analysis",
            command=self.stop_analysis,
            width=120,
            height=35,
            font=ctk.CTkFont(size=11, weight="bold"),
            fg_color=self.colors['danger'],
            hover_color="#A02622",
            text_color="white",
            state="disabled"
        )
        self.stop_btn.pack(side="left", padx=(0, 10))

        # Status indicators
        status_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        status_frame.pack(side="right", padx=20, pady=15)
        
        # AI Status
        self.ai_status = ctk.CTkLabel(
            status_frame,
            text="🤖 AI Ready",
            font=ctk.CTkFont(size=12),
            text_color="white",
            fg_color=self.colors['success'],
            corner_radius=10,
            width=100,
            height=25
        )
        self.ai_status.pack(side="right", padx=(10, 0))
        
        # System Status
        self.system_status = ctk.CTkLabel(
            status_frame,
            text="✅ System Online",
            font=ctk.CTkFont(size=12),
            text_color="white",
            fg_color=self.colors['success'],
            corner_radius=10,
            width=120,
            height=25
        )
        self.system_status.pack(side="right")
    
    def create_main_layout(self):
        """Create main layout with panels"""
        # Main container
        self.main_container = ctk.CTkFrame(
            self.root,
            fg_color=self.colors['background'],
            corner_radius=0
        )
        self.main_container.pack(fill="both", expand=True, padx=0, pady=0)
        
        # Create three-panel layout
        self.left_panel = ctk.CTkFrame(
            self.main_container,
            width=300,
            fg_color=self.colors['surface'],
            corner_radius=10
        )
        self.left_panel.pack(side="left", fill="y", padx=(10, 5), pady=10)
        self.left_panel.pack_propagate(False)
        
        self.center_panel = ctk.CTkFrame(
            self.main_container,
            fg_color=self.colors['surface'],
            corner_radius=10
        )
        self.center_panel.pack(side="left", fill="both", expand=True, padx=5, pady=10)
        
        self.right_panel = ctk.CTkFrame(
            self.main_container,
            width=350,
            fg_color=self.colors['surface'],
            corner_radius=10
        )
        self.right_panel.pack(side="right", fill="y", padx=(5, 10), pady=10)
        self.right_panel.pack_propagate(False)
    
    def create_sidebar(self):
        """Create modern sidebar with controls"""
        # Sidebar header
        sidebar_header = ctk.CTkLabel(
            self.left_panel,
            text="🔬 Analysis Controls",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=self.colors['text_primary']
        )
        sidebar_header.pack(pady=(20, 10))
        
        # Image upload section
        upload_frame = ctk.CTkFrame(self.left_panel, fg_color="transparent")
        upload_frame.pack(fill="x", padx=20, pady=10)
        
        upload_label = ctk.CTkLabel(
            upload_frame,
            text="📸 Image Upload",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['text_primary']
        )
        upload_label.pack(anchor="w")
        
        self.upload_btn = ctk.CTkButton(
            upload_frame,
            text="Select Dermatological Image",
            command=self.upload_image,
            height=40,
            font=ctk.CTkFont(size=12, weight="bold"),
            fg_color=self.colors['primary'],
            hover_color=self.colors['secondary']
        )
        self.upload_btn.pack(fill="x", pady=(5, 0))
        
        # Analysis options
        options_frame = ctk.CTkFrame(self.left_panel, fg_color="transparent")
        options_frame.pack(fill="x", padx=20, pady=20)
        
        options_label = ctk.CTkLabel(
            options_frame,
            text="⚙️ Analysis Options",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['text_primary']
        )
        options_label.pack(anchor="w")
        
        # ABCDE Analysis toggle
        self.abcde_var = ctk.BooleanVar(value=True)
        abcde_check = ctk.CTkCheckBox(
            options_frame,
            text="ABCDE Melanoma Analysis",
            variable=self.abcde_var,
            font=ctk.CTkFont(size=12),
            text_color=self.colors['text_primary']
        )
        abcde_check.pack(anchor="w", pady=(10, 5))
        
        # Multi-condition analysis toggle
        self.multi_var = ctk.BooleanVar(value=True)
        multi_check = ctk.CTkCheckBox(
            options_frame,
            text="Multi-Condition Detection",
            variable=self.multi_var,
            font=ctk.CTkFont(size=12),
            text_color=self.colors['text_primary']
        )
        multi_check.pack(anchor="w", pady=5)
        
        # AI Analysis toggle
        self.ai_var = ctk.BooleanVar(value=True)
        ai_check = ctk.CTkCheckBox(
            options_frame,
            text="Gemma-3n AI Analysis",
            variable=self.ai_var,
            font=ctk.CTkFont(size=12),
            text_color=self.colors['text_primary']
        )
        ai_check.pack(anchor="w", pady=5)

        # Analysis mode selection
        mode_label = ctk.CTkLabel(
            options_frame,
            text="Analysis Mode:",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=self.colors['text_primary']
        )
        mode_label.pack(anchor="w", pady=(15, 5))

        # Analysis mode radio buttons
        self.analysis_mode = ctk.StringVar(value="multi")

        multi_mode_radio = ctk.CTkRadioButton(
            options_frame,
            text="Multi-Condition Analysis (All 14 diseases)",
            variable=self.analysis_mode,
            value="multi",
            font=ctk.CTkFont(size=11),
            text_color=self.colors['text_primary'],
            command=self.on_analysis_mode_change
        )
        multi_mode_radio.pack(anchor="w", pady=2)

        single_mode_radio = ctk.CTkRadioButton(
            options_frame,
            text="Single-Condition Analysis (Choose specific disease)",
            variable=self.analysis_mode,
            value="single",
            font=ctk.CTkFont(size=11),
            text_color=self.colors['text_primary'],
            command=self.on_analysis_mode_change
        )
        single_mode_radio.pack(anchor="w", pady=2)

        # Single condition selection (initially hidden)
        self.condition_frame = ctk.CTkFrame(options_frame, fg_color="transparent")
        self.condition_frame.pack(anchor="w", fill="x", pady=(5, 0))

        condition_label = ctk.CTkLabel(
            self.condition_frame,
            text="Select Condition:",
            font=ctk.CTkFont(size=11, weight="bold"),
            text_color=self.colors['text_primary']
        )
        condition_label.pack(anchor="w", pady=(5, 2))

        # Condition dropdown
        self.condition_options = [
            "Melanoma",
            "Melanocytic Nevi",
            "Basal Cell Carcinoma",
            "Actinic Keratoses",
            "Benign Keratosis-like Lesions",
            "Dermatofibroma",
            "Vascular Lesions",
            "Seborrheic Keratoses",
            "Squamous Cell Carcinoma",
            "Pigmented Benign Keratoses",
            "Atypical Nevi",
            "Lentigo",
            "Solar Lentigo",
            "Healthy Skin"
        ]

        self.selected_condition = ctk.StringVar(value=self.condition_options[0])
        self.condition_dropdown = ctk.CTkComboBox(
            self.condition_frame,
            values=self.condition_options,
            variable=self.selected_condition,
            font=ctk.CTkFont(size=10),
            width=280,
            state="disabled"
        )
        self.condition_dropdown.pack(anchor="w", pady=2)

        # Analysis button
        self.analyze_btn = ctk.CTkButton(
            self.left_panel,
            text="🚀 Start Comprehensive Analysis",
            command=self.start_analysis,
            height=50,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=self.colors['success'],
            hover_color=self.colors['danger'],
            state="disabled"
        )
        self.analyze_btn.pack(fill="x", padx=20, pady=(20, 10))

        # Reset button
        self.reset_btn = ctk.CTkButton(
            self.left_panel,
            text="🔄 Reset & Clear Results",
            command=self.reset_analysis,
            height=40,
            font=ctk.CTkFont(size=12, weight="bold"),
            fg_color=self.colors['secondary'],
            hover_color=self.colors['primary'],
            state="disabled"
        )
        self.reset_btn.pack(fill="x", padx=20, pady=(0, 20))

        # Progress section
        progress_frame = ctk.CTkFrame(self.left_panel, fg_color="transparent")
        progress_frame.pack(fill="x", padx=20, pady=10)
        
        self.progress_label = ctk.CTkLabel(
            progress_frame,
            text="Ready for analysis",
            font=ctk.CTkFont(size=12),
            text_color=self.colors['text_secondary']
        )
        self.progress_label.pack(anchor="w")
        
        self.progress_bar = ctk.CTkProgressBar(
            progress_frame,
            height=8,
            progress_color=self.colors['primary']
        )
        self.progress_bar.pack(fill="x", pady=(5, 0))
        self.progress_bar.set(0)
    
    def create_analysis_panel(self):
        """Create central analysis panel with image display"""
        # Panel header
        analysis_header = ctk.CTkLabel(
            self.center_panel,
            text="🖼️ Image Analysis Workspace",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=self.colors['text_primary']
        )
        analysis_header.pack(pady=(20, 10))
        
        # Image display area
        self.image_frame = ctk.CTkFrame(
            self.center_panel,
            fg_color=self.colors['border'],
            corner_radius=10
        )
        self.image_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # Placeholder for image
        self.image_label = ctk.CTkLabel(
            self.image_frame,
            text="📷\n\nNo image selected\n\nClick 'Select Dermatological Image' to begin",
            font=ctk.CTkFont(size=16),
            text_color=self.colors['text_secondary']
        )
        self.image_label.pack(expand=True)
        
        # Image info panel
        self.info_frame = ctk.CTkFrame(self.center_panel, height=60, fg_color="transparent")
        self.info_frame.pack(fill="x", padx=20, pady=(0, 20))
        self.info_frame.pack_propagate(False)
        
        self.image_info = ctk.CTkLabel(
            self.info_frame,
            text="Image information will appear here",
            font=ctk.CTkFont(size=12),
            text_color=self.colors['text_secondary']
        )
        self.image_info.pack(pady=10)
    
    def create_results_panel(self):
        """Create results panel with detailed analysis"""
        # Panel header
        results_header = ctk.CTkLabel(
            self.right_panel,
            text="📊 Analysis Results",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=self.colors['text_primary']
        )
        results_header.pack(pady=(20, 10))
        
        # Results container with scrolling
        self.results_container = ctk.CTkScrollableFrame(
            self.right_panel,
            fg_color="transparent"
        )
        self.results_container.pack(fill="both", expand=True, padx=20, pady=10)
        
        # Placeholder
        self.results_placeholder = ctk.CTkLabel(
            self.results_container,
            text="🔬\n\nAnalysis results will\nappear here after\nprocessing an image",
            font=ctk.CTkFont(size=14),
            text_color=self.colors['text_secondary']
        )
        self.results_placeholder.pack(pady=50)
    
    def create_footer(self):
        """Create professional footer"""
        footer_frame = ctk.CTkFrame(
            self.root,
            height=40,
            fg_color=self.colors['border'],
            corner_radius=0
        )
        footer_frame.pack(fill="x", side="bottom")
        footer_frame.pack_propagate(False)
        
        # Footer content
        footer_left = ctk.CTkLabel(
            footer_frame,
            text="© 2025 DermatoGemma Multi-Detection System - Professional Medical AI",
            font=ctk.CTkFont(size=10),
            text_color=self.colors['text_secondary']
        )
        footer_left.pack(side="left", padx=20, pady=10)
        
        footer_right = ctk.CTkLabel(
            footer_frame,
            text=f"Session: {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            font=ctk.CTkFont(size=10),
            text_color=self.colors['text_secondary']
        )
        footer_right.pack(side="right", padx=20, pady=10)
    
    def init_analysis_engine(self):
        """Initialize analysis engine"""
        try:
            # Import analysis components
            import sys
            from pathlib import Path
            project_root = Path(__file__).parent
            sys.path.insert(0, str(project_root))
            
            from core.multi_condition_engine import MultiConditionAnalysisEngine
            self.analysis_engine = MultiConditionAnalysisEngine()
            
            self.update_status("✅ Analysis engine ready", "success")
            logger.info("✅ Analysis engine initialized")
            
        except Exception as e:
            self.update_status("❌ Engine initialization failed", "danger")
            logger.error(f"❌ Failed to initialize analysis engine: {e}")
            self.analysis_engine = None
    
    def update_status(self, message: str, status_type: str = "info"):
        """Update status indicators"""
        colors = {
            "success": self.colors['success'],
            "danger": self.colors['danger'],
            "info": self.colors['primary']
        }
        
        self.progress_label.configure(text=message)
        if hasattr(self, 'system_status'):
            self.system_status.configure(
                text=message,
                fg_color=colors.get(status_type, self.colors['primary'])
            )
    
    def upload_image(self):
        """Handle image upload with modern file dialog"""
        file_types = [
            ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif"),
            ("JPEG files", "*.jpg *.jpeg"),
            ("PNG files", "*.png"),
            ("All files", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title="Select Dermatological Image for Analysis",
            filetypes=file_types,
            initialdir=str(Path.home() / "Pictures")
        )
        
        if file_path:
            self.load_image(file_path)
    
    def load_image(self, file_path: str):
        """Load and display image with professional presentation"""
        try:
            # Load image
            image = Image.open(file_path)
            self.current_image = np.array(image)
            
            # Calculate display size maintaining aspect ratio
            display_width = 600
            display_height = 400
            
            img_width, img_height = image.size
            aspect_ratio = img_width / img_height
            
            if aspect_ratio > display_width / display_height:
                new_width = display_width
                new_height = int(display_width / aspect_ratio)
            else:
                new_height = display_height
                new_width = int(display_height * aspect_ratio)
            
            # Resize image for display
            display_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # Convert to PhotoImage
            photo = ImageTk.PhotoImage(display_image)
            
            # Update image display
            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo  # Keep reference
            
            # Update image info
            file_size = Path(file_path).stat().st_size / 1024  # KB
            info_text = f"📁 {Path(file_path).name} | 📐 {img_width}×{img_height} | 💾 {file_size:.1f} KB"
            self.image_info.configure(text=info_text)
            
            # Enable analysis and reset buttons
            self.analyze_btn.configure(state="normal")
            self.reset_btn.configure(state="normal")
            self.update_status("✅ Image loaded successfully", "success")
            
            logger.info(f"✅ Image loaded: {file_path}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load image:\n{str(e)}")
            logger.error(f"❌ Failed to load image: {e}")
    
    def start_analysis(self):
        """Start comprehensive analysis in background thread"""
        if self.current_image is None:
            messagebox.showwarning("Warning", "Please select an image first.")
            return
        
        if self.processing:
            return
        
        # Start analysis in background thread
        analysis_thread = threading.Thread(target=self.run_analysis)
        analysis_thread.daemon = True
        analysis_thread.start()
    
    def run_analysis(self):
        """Run analysis with progress updates"""
        self.processing = True
        self.stop_requested = False
        self.analyze_btn.configure(state="disabled", text="🔄 Processing...")
        self.stop_btn.configure(state="normal")
        
        try:
            # Simulate analysis steps with progress updates
            steps = [
                ("🔍 Initializing analysis...", 0.1),
                ("🖼️ Processing image...", 0.2),
                ("🔬 Detecting lesions...", 0.4),
                ("🎨 ABCDE analysis...", 0.6),
                ("🤖 AI multi-condition analysis...", 0.8),
                ("📊 Generating results...", 0.9),
                ("✅ Analysis complete!", 1.0)
            ]
            
            for step_text, progress in steps:
                # Check if stop was requested
                if self.stop_requested:
                    print("⏹️ DEBUG: Analysis stopped by user during progress")
                    return

                self.root.after(0, lambda t=step_text, p=progress: self.update_progress(t, p))
                time.sleep(1)  # Simulate processing time
            
            # Run actual analysis if engine is available
            if self.analysis_engine:
                # Get analysis mode and condition
                analysis_mode = self.analysis_mode.get()
                selected_condition = self.selected_condition.get() if analysis_mode == "single" else None

                analysis_options = {
                    'enable_abcde_analysis': True,  # Always enable ABCDE
                    'enable_ai_analysis': True,     # Always enable AI/LLM analysis
                    'enable_multi_condition': analysis_mode == "multi",  # Based on selection
                    'enable_single_condition': analysis_mode == "single",  # NEW: Single condition mode
                    'target_condition': selected_condition,  # NEW: Specific condition
                    'enable_temporal_analysis': False,
                    'confidence_threshold': 0.3,
                    'max_lesions_per_image': 10,
                    'risk_threshold_high': 0.7,
                    'risk_threshold_medium': 0.4,
                    'force_llm_analysis': True,    # Force LLM usage
                    'llm_detailed_analysis': True  # Enable detailed LLM analysis
                }
                
                patient_context = {
                    'age': 45,  # Default values
                    'gender': 'unknown',
                    'skin_type': 'unknown'
                }
                
                # Check if stop was requested before starting analysis
                if self.stop_requested:
                    print("⏹️ DEBUG: Analysis stopped by user before engine call")
                    return

                print(f"\n🔍 DEBUG: Starting analysis with engine...")
                print(f"   - Image shape: {self.current_image.shape if hasattr(self.current_image, 'shape') else 'No shape'}")
                print(f"   - Analysis options: {analysis_options}")
                print(f"   - Analysis Mode: {analysis_mode}")
                print(f"   - Target Condition: {selected_condition if analysis_mode == 'single' else 'All conditions'}")
                print(f"   - Multi-condition: {analysis_options.get('enable_multi_condition', False)}")
                print(f"   - Single-condition: {analysis_options.get('enable_single_condition', False)}")
                print(f"   - LLM Analysis ENABLED: {analysis_options.get('enable_ai_analysis', False)}")
                print(f"   - Force LLM: {analysis_options.get('force_llm_analysis', False)}")
                print(f"   - Detailed LLM: {analysis_options.get('llm_detailed_analysis', False)}")

                # Check if LLM is available
                if hasattr(self.analysis_engine, 'gemma_handler'):
                    llm_available = self.analysis_engine.gemma_handler.initialized
                    print(f"   - LLM Handler Available: {llm_available}")
                    if llm_available:
                        print(f"   - LLM Model: {self.analysis_engine.gemma_handler.model_name}")
                        print(f"   - LLM URL: {self.analysis_engine.gemma_handler.ollama_url}")
                else:
                    print(f"   - LLM Handler: NOT FOUND")

                results = self.analysis_engine.analyze_comprehensive(
                    self.current_image,
                    patient_context=patient_context,
                    analysis_options=analysis_options
                )

                print(f"🔍 DEBUG: Analysis completed, results received:")
                print(f"   - Results type: {type(results)}")
                print(f"   - Results keys: {list(results.keys()) if isinstance(results, dict) else 'Not a dict'}")
                print(f"   - Success: {results.get('success', 'N/A') if isinstance(results, dict) else 'N/A'}")

                self.analysis_results = results
                print(f"🔍 DEBUG: Results stored in self.analysis_results, calling display_results...")
                self.root.after(0, self.display_results)
            else:
                # Show demo results
                self.root.after(0, self.display_demo_results)
            
        except Exception as e:
            self.root.after(0, lambda: self.update_status(f"❌ Analysis failed: {str(e)}", "danger"))
            logger.error(f"❌ Analysis failed: {e}")
        
        finally:
            self.processing = False
            self.stop_requested = False
            self.root.after(0, lambda: self.analyze_btn.configure(
                state="normal",
                text="🚀 Start Comprehensive Analysis"
            ))
            self.root.after(0, lambda: self.reset_btn.configure(state="normal"))
            self.root.after(0, lambda: self.stop_btn.configure(state="disabled"))
    
    def reset_analysis(self):
        """Reset the analysis interface and clear all results"""
        try:
            print("\n🔄 RESET: Clearing analysis interface...")

            # Clear current image
            self.current_image = None

            # Clear analysis results
            self.analysis_results = None

            # Reset image display
            if hasattr(self, 'image_label'):
                self.image_label.configure(image=None, text="📷 No image selected\n\nClick 'Load Image' to begin analysis")

            # Clear image info
            if hasattr(self, 'image_info'):
                self.image_info.configure(text="No image loaded")

            # Clear results panel
            if hasattr(self, 'results_container'):
                for widget in self.results_container.winfo_children():
                    widget.destroy()

                # Add placeholder message
                placeholder_label = ctk.CTkLabel(
                    self.results_container,
                    text="📊 Analysis results will appear here after processing an image.\n\nLoad an image and start analysis to see detailed results.",
                    font=ctk.CTkFont(size=14),
                    text_color=self.colors['text_secondary'],
                    justify="center"
                )
                placeholder_label.pack(pady=50)

            # Reset progress bar
            if hasattr(self, 'progress_bar'):
                self.progress_bar.set(0)

            # Reset progress label
            if hasattr(self, 'progress_label'):
                self.progress_label.configure(text="Ready for analysis")

            # Reset button states
            self.analyze_btn.configure(state="disabled", text="🚀 Start Comprehensive Analysis")
            self.reset_btn.configure(state="disabled")

            # Reset processing flag
            self.processing = False

            # Update status
            self.update_status("🔄 Interface reset - ready for new analysis", "info")

            print("✅ RESET: Interface cleared successfully")
            logger.info("🔄 Analysis interface reset")

        except Exception as e:
            print(f"❌ RESET: Reset failed: {e}")
            logger.error(f"❌ Reset failed: {e}")
            self.update_status("❌ Reset failed", "danger")

    def clear_all_screens(self):
        """Clear all screens and reset interface to initial state"""
        try:
            print("\n🧹 CLEAR: Clearing all screens and resetting interface...")

            # Clear current image
            self.current_image = None

            # Clear analysis results
            self.analysis_results = None

            # Reset image display
            if hasattr(self, 'image_label'):
                self.image_label.configure(
                    image=None,
                    text="📷 No image selected\n\nClick 'Load Image' to begin analysis"
                )

            # Clear image info
            if hasattr(self, 'image_info'):
                self.image_info.configure(text="No image loaded")

            # Clear results panel
            if hasattr(self, 'results_container'):
                for widget in self.results_container.winfo_children():
                    widget.destroy()

                # Add placeholder message
                placeholder_label = ctk.CTkLabel(
                    self.results_container,
                    text="📊 Analysis results will appear here after processing an image.\n\nLoad an image and start analysis to see detailed results.",
                    font=ctk.CTkFont(size=14),
                    text_color=self.colors['text_secondary'],
                    justify="center"
                )
                placeholder_label.pack(pady=50)

            # Reset progress bar
            if hasattr(self, 'progress_bar'):
                self.progress_bar.set(0)

            # Reset progress label
            if hasattr(self, 'progress_label'):
                self.progress_label.configure(text="Ready for analysis")

            # Reset button states
            self.analyze_btn.configure(state="disabled", text="🚀 Start Comprehensive Analysis")
            self.reset_btn.configure(state="disabled")
            self.stop_btn.configure(state="disabled")

            # Reset analysis mode to default
            self.analysis_mode.set("multi")
            self.condition_dropdown.configure(state="disabled")
            self.selected_condition.set(self.condition_options[0])

            # Reset processing flag
            self.processing = False

            # Update status
            self.update_status("🧹 All screens cleared - ready for new analysis", "success")

            print("✅ CLEAR: All screens cleared successfully")
            logger.info("🧹 All screens cleared and interface reset")

        except Exception as e:
            print(f"❌ CLEAR: Clear screens failed: {e}")
            logger.error(f"❌ Clear screens failed: {e}")
            self.update_status("❌ Clear screens failed", "danger")

    def stop_analysis(self):
        """Stop current analysis process"""
        try:
            print("\n⏹️ STOP: Stopping current analysis...")

            if not self.processing:
                print("⚠️ STOP: No analysis currently running")
                self.update_status("⚠️ No analysis currently running", "warning")
                return

            # Set stop flag
            self.processing = False
            self.stop_requested = True

            # Reset button states
            self.analyze_btn.configure(state="normal", text="🚀 Start Comprehensive Analysis")
            self.stop_btn.configure(state="disabled")

            # Reset progress
            self.progress_bar.set(0)
            self.progress_label.configure(text="Analysis stopped by user")

            # Update status
            self.update_status("⏹️ Analysis stopped by user", "warning")

            print("✅ STOP: Analysis stopped successfully")
            logger.info("⏹️ Analysis stopped by user request")

        except Exception as e:
            print(f"❌ STOP: Stop analysis failed: {e}")
            logger.error(f"❌ Stop analysis failed: {e}")
            self.update_status("❌ Stop analysis failed", "danger")

    def on_analysis_mode_change(self):
        """Handle analysis mode change"""
        try:
            mode = self.analysis_mode.get()

            if mode == "single":
                # Enable single condition selection
                self.condition_dropdown.configure(state="normal")
                print(f"🔍 DEBUG: Single-condition mode selected")
            else:
                # Disable single condition selection
                self.condition_dropdown.configure(state="disabled")
                print(f"🔍 DEBUG: Multi-condition mode selected")

        except Exception as e:
            logger.error(f"❌ Analysis mode change failed: {e}")

    def update_progress(self, text: str, progress: float):
        """Update progress bar and text"""
        self.progress_label.configure(text=text)
        self.progress_bar.set(progress)
    
    def display_results(self):
        """Display comprehensive analysis results"""
        # Clear previous results
        for widget in self.results_container.winfo_children():
            widget.destroy()

        # Debug: Print analysis results to console
        print(f"\n🔍 DEBUG: Analysis results received in UI:")
        print(f"   - Success: {self.analysis_results.get('success', 'N/A') if self.analysis_results else 'No results'}")
        print(f"   - Keys: {list(self.analysis_results.keys()) if self.analysis_results else 'No keys'}")
        if self.analysis_results:
            print(f"   - Processing time: {self.analysis_results.get('processing_time', 'N/A')}")
            print(f"   - No lesions detected: {self.analysis_results.get('no_lesions_detected', 'N/A')}")
        print(f"🔍 DEBUG: Displaying results in GUI now...\n")
        
        if not self.analysis_results:
            # Show placeholder message
            placeholder_label = ctk.CTkLabel(
                self.results_container,
                text="No analysis results available. Please run an analysis first.",
                font=ctk.CTkFont(size=14),
                text_color=self.colors['text_secondary']
            )
            placeholder_label.pack(pady=50)
            return

        # Check if analysis failed
        if not self.analysis_results.get('success', False):
            error_frame = ctk.CTkFrame(self.results_container, fg_color=self.colors['error'])
            error_frame.pack(fill="x", pady=10, padx=15)

            error_label = ctk.CTkLabel(
                error_frame,
                text=f"❌ Analysis Failed: {self.analysis_results.get('error', 'Unknown error')}",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color="white",
                wraplength=400
            )
            error_label.pack(padx=15, pady=10)
            return

        # Results header
        header = ctk.CTkLabel(
            self.results_container,
            text="🎯 Comprehensive Analysis Results",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=self.colors['text_primary']
        )
        header.pack(pady=(0, 20))

        # Check if no lesions detected
        if self.analysis_results.get('no_lesions_detected', False):
            success_frame = ctk.CTkFrame(self.results_container, fg_color=self.colors['success'])
            success_frame.pack(fill="x", pady=10, padx=15)

            success_label = ctk.CTkLabel(
                success_frame,
                text="🟢 NO SKIN LESIONS DETECTED\nYour skin appears healthy in the analyzed image.",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color="white",
                wraplength=400
            )
            success_label.pack(padx=15, pady=10)

            # Show recommendations for healthy skin
            self.create_clinical_recommendations()
            return

        # Display results sections with correct data structure
        self.create_detection_summary()
        self.create_risk_assessment()
        self.create_conditions_detected()
        self.create_clinical_recommendations()
        self.create_confidence_metrics()
    
    def display_demo_results(self):
        """Display demo results when engine is not available"""
        # Clear previous results
        for widget in self.results_container.winfo_children():
            widget.destroy()
        
        # Demo results
        demo_label = ctk.CTkLabel(
            self.results_container,
            text="🎯 Demo Analysis Results\n\n✅ 3 lesions detected\n🎨 ABCDE scores calculated\n🤖 AI analysis completed\n⚠️ Low risk assessment\n📋 4 recommendations generated",
            font=ctk.CTkFont(size=14),
            text_color=self.colors['text_primary'],
            justify="left"
        )
        demo_label.pack(pady=20)
    
    def create_results_section(self, title: str, data: Any):
        """Create a results section with modern styling"""
        section_frame = ctk.CTkFrame(self.results_container, fg_color=self.colors['background'])
        section_frame.pack(fill="x", pady=10)
        
        # Section title
        title_label = ctk.CTkLabel(
            section_frame,
            text=title,
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['text_primary']
        )
        title_label.pack(anchor="w", padx=15, pady=(10, 5))
        
        # Section content
        if isinstance(data, dict) and data:
            for key, value in data.items():
                if isinstance(value, (int, float)):
                    content_text = f"{key}: {value:.2f}" if isinstance(value, float) else f"{key}: {value}"
                else:
                    content_text = f"{key}: {str(value)[:50]}..."
                
                content_label = ctk.CTkLabel(
                    section_frame,
                    text=content_text,
                    font=ctk.CTkFont(size=12),
                    text_color=self.colors['text_secondary']
                )
                content_label.pack(anchor="w", padx=25, pady=2)
        
        elif isinstance(data, list) and data:
            for i, item in enumerate(data[:3]):  # Show first 3 items
                if isinstance(item, dict):
                    content_text = f"Item {i+1}: {len(item)} properties"
                else:
                    content_text = f"Item {i+1}: {str(item)[:50]}..."
                
                content_label = ctk.CTkLabel(
                    section_frame,
                    text=content_text,
                    font=ctk.CTkFont(size=12),
                    text_color=self.colors['text_secondary']
                )
                content_label.pack(anchor="w", padx=25, pady=2)
        
        else:
            no_data_label = ctk.CTkLabel(
                section_frame,
                text="No data available",
                font=ctk.CTkFont(size=12),
                text_color=self.colors['text_secondary']
            )
            no_data_label.pack(anchor="w", padx=25, pady=(5, 10))

    def create_detection_summary(self):
        """Create detection summary section"""
        section_frame = ctk.CTkFrame(self.results_container, fg_color=self.colors['background'])
        section_frame.pack(fill="x", pady=10)

        # Section title
        title_label = ctk.CTkLabel(
            section_frame,
            text="🔍 DETECTION SUMMARY",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['text_primary']
        )
        title_label.pack(anchor="w", padx=15, pady=(10, 5))

        # Get detection data
        metadata = self.analysis_results.get('analysis_metadata', {})
        total_lesions = metadata.get('total_lesions_detected', 0)
        analyzed_lesions = metadata.get('lesions_analyzed', 0)
        processing_time = self.analysis_results.get('processing_time', 0.0)

        # Display detection info
        info_items = [
            f"Total lesions detected: {total_lesions}",
            f"Lesions analyzed: {analyzed_lesions}",
            f"Processing time: {processing_time:.2f} seconds"
        ]

        for item in info_items:
            content_label = ctk.CTkLabel(
                section_frame,
                text=f"• {item}",
                font=ctk.CTkFont(size=12),
                text_color=self.colors['text_secondary']
            )
            content_label.pack(anchor="w", padx=25, pady=2)

    def create_risk_assessment(self):
        """Create risk assessment section"""
        section_frame = ctk.CTkFrame(self.results_container, fg_color=self.colors['background'])
        section_frame.pack(fill="x", pady=10)

        # Section title
        title_label = ctk.CTkLabel(
            section_frame,
            text="🎯 RISK ASSESSMENT",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['text_primary']
        )
        title_label.pack(anchor="w", padx=15, pady=(10, 5))

        # Get risk data
        overall_assessment = self.analysis_results.get('overall_assessment', {})
        risk_assessment = self.analysis_results.get('risk_assessment', {})

        overall_risk = overall_assessment.get('overall_risk_level', 'unknown').upper()
        clinical_urgency = risk_assessment.get('clinical_urgency', 'routine').upper()
        follow_up = risk_assessment.get('follow_up_recommended', False)

        # Risk level with color coding
        risk_color = self.colors['success'] if overall_risk == 'LOW' else \
                    self.colors['warning'] if overall_risk == 'MODERATE' else \
                    self.colors['danger'] if overall_risk == 'HIGH' else \
                    self.colors['text_secondary']

        risk_label = ctk.CTkLabel(
            section_frame,
            text=f"Overall Risk Level: {overall_risk}",
            font=ctk.CTkFont(size=13, weight="bold"),
            text_color=risk_color
        )
        risk_label.pack(anchor="w", padx=25, pady=5)

        # Additional risk info
        risk_items = [
            f"Clinical Urgency: {clinical_urgency}",
            f"Follow-up Required: {'YES' if follow_up else 'NO'}"
        ]

        for item in risk_items:
            content_label = ctk.CTkLabel(
                section_frame,
                text=f"• {item}",
                font=ctk.CTkFont(size=12),
                text_color=self.colors['text_secondary']
            )
            content_label.pack(anchor="w", padx=25, pady=2)

    def create_conditions_detected(self):
        """Create conditions detected section"""
        section_frame = ctk.CTkFrame(self.results_container, fg_color=self.colors['background'])
        section_frame.pack(fill="x", pady=10)

        # Section title
        title_label = ctk.CTkLabel(
            section_frame,
            text="🔬 CONDITIONS DETECTED",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['text_primary']
        )
        title_label.pack(anchor="w", padx=15, pady=(10, 5))

        # Get conditions data
        overall_assessment = self.analysis_results.get('overall_assessment', {})
        condition_detections = overall_assessment.get('condition_detections', {})

        if condition_detections:
            # Sort conditions by count/probability
            sorted_conditions = sorted(condition_detections.items(),
                                     key=lambda x: x[1] if isinstance(x[1], (int, float)) else 0,
                                     reverse=True)

            conditions_shown = 0
            for condition, count in sorted_conditions[:5]:
                if isinstance(count, (int, float)) and count > 0:
                    condition_name = condition.replace('_', ' ').title()
                    content_label = ctk.CTkLabel(
                        section_frame,
                        text=f"• {condition_name}: {count}",
                        font=ctk.CTkFont(size=12),
                        text_color=self.colors['text_secondary']
                    )
                    content_label.pack(anchor="w", padx=25, pady=2)
                    conditions_shown += 1

            if conditions_shown == 0:
                no_conditions_label = ctk.CTkLabel(
                    section_frame,
                    text="• No specific conditions detected with high confidence",
                    font=ctk.CTkFont(size=12),
                    text_color=self.colors['text_secondary']
                )
                no_conditions_label.pack(anchor="w", padx=25, pady=2)
        else:
            # Try to get conditions from lesion analyses
            lesion_analyses = self.analysis_results.get('lesion_analyses', [])
            conditions_found = {}

            for analysis in lesion_analyses:
                if isinstance(analysis, dict) and analysis.get('success', False):
                    condition_probs = analysis.get('condition_probabilities', {})
                    for condition_id, prob_data in condition_probs.items():
                        if isinstance(prob_data, dict):
                            prob = prob_data.get('probability', 0.0)
                            if prob > 0.3:  # Show conditions with >30% probability
                                conditions_found[condition_id] = conditions_found.get(condition_id, 0) + 1

            if conditions_found:
                sorted_found = sorted(conditions_found.items(), key=lambda x: x[1], reverse=True)
                for condition, count in sorted_found[:5]:
                    condition_name = condition.replace('_', ' ').title()
                    content_label = ctk.CTkLabel(
                        section_frame,
                        text=f"• {condition_name}: {count} lesion(s)",
                        font=ctk.CTkFont(size=12),
                        text_color=self.colors['text_secondary']
                    )
                    content_label.pack(anchor="w", padx=25, pady=2)
            else:
                analysis_label = ctk.CTkLabel(
                    section_frame,
                    text="• Analysis completed - see recommendations below",
                    font=ctk.CTkFont(size=12),
                    text_color=self.colors['text_secondary']
                )
                analysis_label.pack(anchor="w", padx=25, pady=2)

    def create_clinical_recommendations(self):
        """Create clinical recommendations section"""
        section_frame = ctk.CTkFrame(self.results_container, fg_color=self.colors['background'])
        section_frame.pack(fill="x", pady=10)

        # Section title
        title_label = ctk.CTkLabel(
            section_frame,
            text="📋 CLINICAL RECOMMENDATIONS",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['text_primary']
        )
        title_label.pack(anchor="w", padx=15, pady=(10, 5))

        # Get recommendations
        clinical_recommendations = self.analysis_results.get('clinical_recommendations', [])

        if clinical_recommendations:
            for i, rec in enumerate(clinical_recommendations[:6], 1):
                # Handle both string and dict recommendations
                if isinstance(rec, dict):
                    clean_rec = rec.get('recommendation', rec.get('text', str(rec)))
                else:
                    clean_rec = str(rec).replace('🔴', '').replace('🟠', '').replace('🟡', '').replace('🟢', '').strip()

                content_label = ctk.CTkLabel(
                    section_frame,
                    text=f"{i}. {clean_rec}",
                    font=ctk.CTkFont(size=12),
                    text_color=self.colors['text_secondary'],
                    wraplength=400
                )
                content_label.pack(anchor="w", padx=25, pady=2)
        else:
            no_rec_label = ctk.CTkLabel(
                section_frame,
                text="• No specific recommendations available",
                font=ctk.CTkFont(size=12),
                text_color=self.colors['text_secondary']
            )
            no_rec_label.pack(anchor="w", padx=25, pady=2)

    def create_confidence_metrics(self):
        """Create confidence metrics section"""
        section_frame = ctk.CTkFrame(self.results_container, fg_color=self.colors['background'])
        section_frame.pack(fill="x", pady=10)

        # Section title
        title_label = ctk.CTkLabel(
            section_frame,
            text="📊 ANALYSIS CONFIDENCE",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['text_primary']
        )
        title_label.pack(anchor="w", padx=15, pady=(10, 5))

        # Get confidence data
        confidence_metrics = self.analysis_results.get('confidence_metrics', {})
        overall_confidence = confidence_metrics.get('overall_confidence', 0.0)
        analysis_quality = confidence_metrics.get('analysis_quality', 'unknown').upper()

        # Display confidence info
        confidence_items = [
            f"Overall Confidence: {overall_confidence:.1%}",
            f"Analysis Quality: {analysis_quality}"
        ]

        for item in confidence_items:
            content_label = ctk.CTkLabel(
                section_frame,
                text=f"• {item}",
                font=ctk.CTkFont(size=12),
                text_color=self.colors['text_secondary']
            )
            content_label.pack(anchor="w", padx=25, pady=2)

        # Add disclaimer
        disclaimer_frame = ctk.CTkFrame(section_frame, fg_color=self.colors['warning'], corner_radius=8)
        disclaimer_frame.pack(fill="x", padx=15, pady=(10, 15))

        disclaimer_label = ctk.CTkLabel(
            disclaimer_frame,
            text="⚠️ IMPORTANT: This is an AI-assisted analysis, not a medical diagnosis.\nAlways consult a qualified dermatologist for professional evaluation.",
            font=ctk.CTkFont(size=11),
            text_color="black",
            wraplength=400
        )
        disclaimer_label.pack(padx=10, pady=8)

    def run(self):
        """Start the modern UI application"""
        logger.info("🚀 Starting Modern DermatoGemma UI")
        self.root.mainloop()

def main():
    """Main function to run the modern UI"""
    try:
        app = ModernDermatoGemmaUI()
        app.run()
    except Exception as e:
        logger.error(f"❌ Failed to start UI: {e}")
        messagebox.showerror("Error", f"Failed to start application:\n{str(e)}")

if __name__ == "__main__":
    main()
