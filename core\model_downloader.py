"""
Hugging Face Model Downloader for DermatoGemma Multi-Detection System
Downloads Gemma 3n model from Hugging Face with progress tracking
Based on RetinoblastoGemma-App implementation
"""
import os
import logging
import time
from pathlib import Path
from typing import Optional, Callable, Dict, Any
import requests
from tqdm import tqdm
import threading
import json

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()  # Load .env file if it exists
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False

try:
    from huggingface_hub import hf_hub_download, repo_info, HfApi
    from huggingface_hub.utils import RepositoryNotFoundError, GatedRepoError
    HF_HUB_AVAILABLE = True
except ImportError:
    HF_HUB_AVAILABLE = False

logger = logging.getLogger(__name__)

class HuggingFaceModelDownloader:
    """Downloads Gemma model from Hugging Face with progress tracking"""

    # Available Gemma-3n models - Exactly as requested
    AVAILABLE_MODELS = {
        "E2B": {
            "id": "google/gemma-3n-E2B-it",
            "name": "Gemma-3n E2B",
            "description": "2-bit quantized model for lightweight deployment",
            "size": "~1.2GB",
            "recommended": False
        },
        "E4B": {
            "id": "google/gemma-3n-E4B-it",
            "name": "Gemma-3n E4B",
            "description": "4-bit quantized model for balanced performance",
            "size": "~2.5GB",
            "recommended": True
        }
    }

    def __init__(self, model_id: Optional[str] = None, model_key: Optional[str] = None, token: Optional[str] = None):
        # Load environment variables if not already loaded
        if DOTENV_AVAILABLE:
            load_dotenv()

        # Determine model to use
        if model_key and model_key in self.AVAILABLE_MODELS:
            # Use specified model key
            self.model_key = model_key
            self.model_config = self.AVAILABLE_MODELS[model_key]
            self.model_id = self.model_config["id"]
        elif model_id:
            # Use specified model ID directly
            self.model_id = model_id
            self.model_key = self._find_model_key_by_id(model_id)
            self.model_config = self.AVAILABLE_MODELS.get(self.model_key, {}) if self.model_key else {}
        else:
            # Use default (recommended model)
            self.model_key = self._get_recommended_model()
            self.model_config = self.AVAILABLE_MODELS[self.model_key]
            self.model_id = self.model_config["id"]

        # Try to get token from multiple sources in order of preference
        self.token = (
            token or                           # 1. Explicitly passed token
            os.getenv("HUGGINGFACE_TOKEN") or  # 2. Environment variable
            self._load_token_from_env_file()   # 3. .env file (manual fallback)
        )

        # Allow model directory to be configured via environment
        models_dir_env = os.getenv("MODEL_CACHE_DIR", "models")
        self.models_dir = Path(models_dir_env)

        # Create model-specific directory
        if self.model_key:
            self.model_dir = self.models_dir / f"gemma-3n-{self.model_key}"
        else:
            self.model_dir = self.models_dir / "gemma-3n"
        self.download_cancelled = False
        self.download_thread = None

        # Fallback models if the primary one is not accessible
        self.fallback_models = [
            "google/gemma-2b-it",  # Public instruction-tuned model
            "google/gemma-2b",     # Public base model
            "google/gemma-7b-it",  # Larger instruction-tuned model
        ]

        # Check if HF Hub is available
        if not HF_HUB_AVAILABLE:
            logger.error("huggingface_hub library not available. Please install it: pip install huggingface_hub")
            raise ImportError("huggingface_hub library required for model downloading")

        # Ensure models directory exists
        self.models_dir.mkdir(exist_ok=True)
        self.model_dir.mkdir(exist_ok=True)

        logger.info(f"Initialized HuggingFace downloader for {model_id}")
        logger.info(f"Target directory: {self.model_dir}")

        # Check if token is available and log source
        if self.token:
            token_source = self._get_token_source()
            logger.info(f"Using Hugging Face authentication token from {token_source}")
        else:
            logger.warning("No Hugging Face token found. Some models may require authentication.")
            logger.info("💡 To set up authentication:")
            logger.info("   1. Create a .env file with: HUGGINGFACE_TOKEN=your_token_here")
            logger.info("   2. Or set environment variable: HUGGINGFACE_TOKEN=your_token_here")
            logger.info("   3. Or run: python setup_authentication.py")

    def _find_model_key_by_id(self, model_id: str) -> Optional[str]:
        """Find model key by model ID"""
        for key, config in self.AVAILABLE_MODELS.items():
            if config["id"] == model_id:
                return key
        return None

    def _get_recommended_model(self) -> str:
        """Get the recommended model key"""
        for key, config in self.AVAILABLE_MODELS.items():
            if config.get("recommended", False):
                return key
        # Fallback to first model if no recommended
        return list(self.AVAILABLE_MODELS.keys())[0]

    @classmethod
    def list_available_models(cls) -> Dict[str, Dict]:
        """List all available Gemma-3n models"""
        return cls.AVAILABLE_MODELS.copy()

    @classmethod
    def get_model_info(cls, model_key: str) -> Optional[Dict]:
        """Get information about a specific model"""
        return cls.AVAILABLE_MODELS.get(model_key)

    def get_current_model_info(self) -> Dict[str, Any]:
        """Get information about the currently selected model"""
        info = {
            "model_key": self.model_key,
            "model_id": self.model_id,
            "model_directory": str(self.model_dir),
            "exists": self.check_model_exists(),
            "files": []
        }

        if self.model_config:
            info.update({
                "name": self.model_config.get("name", "Unknown"),
                "description": self.model_config.get("description", ""),
                "size": self.model_config.get("size", "Unknown"),
                "main_file": self.model_config.get("main_file", "")
            })

        if self.model_dir.exists():
            for file_path in self.model_dir.iterdir():
                if file_path.is_file():
                    info["files"].append({
                        "name": file_path.name,
                        "size_mb": file_path.stat().st_size / (1024 * 1024),
                        "path": str(file_path)
                    })

        return info

    def _load_token_from_env_file(self) -> Optional[str]:
        """Manually load token from .env file if dotenv is not available"""
        env_file = Path(".env")
        if not env_file.exists():
            return None

        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith("HUGGINGFACE_TOKEN="):
                        token = line.split("=", 1)[1].strip()
                        # Remove quotes if present
                        if token.startswith('"') and token.endswith('"'):
                            token = token[1:-1]
                        elif token.startswith("'") and token.endswith("'"):
                            token = token[1:-1]
                        return token if token else None
        except Exception as e:
            logger.warning(f"Could not read .env file: {e}")

        return None

    def _get_token_source(self) -> str:
        """Determine where the token came from for logging purposes"""
        if os.getenv("HUGGINGFACE_TOKEN"):
            return "environment variable"
        elif Path(".env").exists():
            return ".env file"
        else:
            return "parameter"

    def check_model_exists(self) -> bool:
        """Check if the model is already downloaded and complete"""
        if not self.model_dir.exists():
            return False

        # Look for any model files that could work
        model_patterns = ['*.task', '*.tflite', '*.bin', '*.safetensors']
        model_files = []

        for pattern in model_patterns:
            model_files.extend(list(self.model_dir.glob(pattern)))

        if not model_files:
            logger.info("No model files found")
            return False

        # Check if at least one file is not empty
        for model_file in model_files:
            if model_file.stat().st_size > 0:
                logger.info(f"✅ Found model file: {model_file.name} ({model_file.stat().st_size / (1024*1024):.1f} MB)")
                return True

        logger.info("Model files exist but are empty")
        return False

    def download_model(self, progress_callback: Optional[Callable[[float, str], None]] = None) -> bool:
        """
        Download the Gemma 3n model from Hugging Face using the official Hub library

        Args:
            progress_callback: Optional callback function that receives (progress: float, status: str)

        Returns:
            bool: True if download successful, False otherwise
        """
        if self.check_model_exists():
            if progress_callback:
                progress_callback(1.0, "Model already exists")
            return True

        self.download_cancelled = False

        try:
            # First, let's check if the repository exists and what files are available
            if progress_callback:
                progress_callback(0.1, "Checking repository...")

            api = HfApi()

            try:
                # Get repository info to see what files are available
                repo_files = api.list_repo_files(self.model_id, token=self.token)
                logger.info(f"Available files in repository: {repo_files}")
            except (RepositoryNotFoundError, GatedRepoError) as e:
                logger.error(f"Repository access error: {e}")
                if progress_callback:
                    progress_callback(0.0, f"Repository access error: {str(e)}")
                return False

            # ESSENTIAL FILES: Download model files + essential configs for execution
            model_files_to_download = []
            essential_config_files = []

            # Find model files (.safetensors, .bin, .task, .tflite)
            # Find essential config files needed for execution
            for file_name in repo_files:
                if any(ext in file_name.lower() for ext in ['.safetensors', '.bin', '.task', '.tflite']):
                    model_files_to_download.append(file_name)
                elif any(name in file_name.lower() for name in ['config.json', 'tokenizer.json', 'tokenizer.model', 'generation_config.json', 'tokenizer_config.json', 'special_tokens_map.json']):
                    essential_config_files.append(file_name)

            if not model_files_to_download:
                logger.error("No model files found in repository")
                if progress_callback:
                    progress_callback(0.0, "No model files found")
                # Continue to fallback logic below
            else:
                # ESSENTIAL FILES: Download model files + essential configs
                all_essential_files = model_files_to_download + essential_config_files
                downloaded_files = []
                failed_files = []
                total_files = len(all_essential_files)

                logger.info(f"📦 ESSENTIAL FILES DOWNLOAD: Found {len(model_files_to_download)} model files and {len(essential_config_files)} essential config files")
                logger.info(f"🎯 Downloading model files + essential configs for execution")

                for i, file_name in enumerate(all_essential_files):
                    progress = 0.2 + (0.7 * i / total_files)  # Progress from 20% to 90%

                    file_type = "model" if file_name in model_files_to_download else "config"
                    if progress_callback:
                        progress_callback(progress, f"Downloading {file_name} [{file_type}] ({i+1}/{total_files})...")

                    try:
                        # Disable Xet Storage to avoid TLS handshake issues
                        import os
                        old_xet_value = os.environ.get('HF_HUB_DISABLE_XET', None)
                        os.environ['HF_HUB_DISABLE_XET'] = '1'

                        downloaded_path = hf_hub_download(
                            repo_id=self.model_id,
                            filename=file_name,
                            local_dir=self.model_dir,
                            token=self.token,
                            local_dir_use_symlinks=False,
                            resume_download=True  # Enable resume for interrupted downloads
                        )

                        # Restore original Xet setting
                        if old_xet_value is None:
                            os.environ.pop('HF_HUB_DISABLE_XET', None)
                        else:
                            os.environ['HF_HUB_DISABLE_XET'] = old_xet_value
                        logger.info(f"✅ Successfully downloaded {file_name} [{file_type}]")
                        downloaded_files.append(file_name)

                    except Exception as e:
                        logger.warning(f"❌ Failed to download {file_name}: {e}")
                        failed_files.append(file_name)
                        # Continue with other files
                        continue

                if downloaded_files:
                    success_rate = len(downloaded_files) / total_files * 100

                    # Count file types
                    downloaded_model_files = [f for f in downloaded_files if f in model_files_to_download]
                    downloaded_config_files = [f for f in downloaded_files if f in essential_config_files]

                    if progress_callback:
                        progress_callback(1.0, f"Essential files download finished! {len(downloaded_files)}/{total_files} files ({success_rate:.1f}%)")

                    logger.info(f"✅ ESSENTIAL FILES DOWNLOAD SUMMARY:")
                    logger.info(f"   📥 Successfully downloaded: {len(downloaded_files)}/{total_files} files ({success_rate:.1f}%)")
                    logger.info(f"   📦 Model files: {len(downloaded_model_files)}")
                    logger.info(f"   ⚙️ Essential config files: {len(downloaded_config_files)}")
                    logger.info(f"   🎯 Ready for model execution!")

                    if failed_files:
                        logger.warning(f"   ⚠️ Failed files: {len(failed_files)} - {failed_files[:3]}{'...' if len(failed_files) > 3 else ''}")

                    return True
                else:
                    logger.error("❌ ESSENTIAL FILES DOWNLOAD FAILED: No files downloaded successfully")
                    if progress_callback:
                        progress_callback(0.0, "Essential files download failed - no files downloaded")
                    # Continue to fallback logic below

                # Check if this is an authentication error
                if "401" in str(e) or "gated" in str(e).lower() or "restricted" in str(e).lower():
                    logger.info("Model appears to be gated/restricted. Trying fallback models...")

                    # Try fallback models
                    for fallback_model in self.fallback_models:
                        try:
                            logger.info(f"Trying fallback model: {fallback_model}")

                            # Get files from fallback model
                            fallback_files = api.list_repo_files(fallback_model, token=self.token)

                            # Look for model files
                            model_files = [f for f in fallback_files if f.endswith(('.bin', '.safetensors', '.task', '.tflite'))]

                            if model_files:
                                # Download the first model file
                                model_file = model_files[0]

                                if progress_callback:
                                    progress_callback(0.5, f"Downloading {model_file} from {fallback_model}...")

                                downloaded_path = hf_hub_download(
                                    repo_id=fallback_model,
                                    filename=model_file,
                                    local_dir=self.model_dir,
                                    token=self.token,
                                    local_dir_use_symlinks=False
                                )

                                logger.info(f"✅ Successfully downloaded {model_file} from {fallback_model}")

                                # Also try to download config files
                                for config_file in ['config.json', 'tokenizer.json', 'tokenizer_config.json']:
                                    if config_file in fallback_files:
                                        try:
                                            hf_hub_download(
                                                repo_id=fallback_model,
                                                filename=config_file,
                                                local_dir=self.model_dir,
                                                token=self.token,
                                                local_dir_use_symlinks=False
                                            )
                                            logger.info(f"✅ Downloaded {config_file}")
                                        except:
                                            logger.warning(f"Could not download {config_file}")

                                if progress_callback:
                                    progress_callback(1.0, f"Downloaded model from {fallback_model}!")

                                # Update the model_id to reflect what we actually downloaded
                                self.model_id = fallback_model
                                logger.info(f"Using fallback model: {fallback_model}")

                                return True

                        except Exception as fallback_error:
                            logger.warning(f"Fallback model {fallback_model} also failed: {fallback_error}")
                            continue

                    logger.error("All fallback models failed")
                    if progress_callback:
                        progress_callback(0.0, "All models failed - authentication required")
                    return False

                else:
                    logger.error(f"Download failed with error: {e}")
                    if progress_callback:
                        progress_callback(0.0, f"Download failed: {str(e)}")
                    return False

        except Exception as e:
            logger.error(f"Unexpected error during download: {e}")
            if progress_callback:
                progress_callback(0.0, f"Unexpected error: {str(e)}")
            return False

    def download_model_async(self, progress_callback: Optional[Callable[[float, str], None]] = None) -> threading.Thread:
        """Start model download in a separate thread"""
        def download_worker():
            self.download_model(progress_callback)

        self.download_thread = threading.Thread(target=download_worker, daemon=True)
        self.download_thread.start()
        return self.download_thread

    def cancel_download(self):
        """Cancel ongoing download"""
        self.download_cancelled = True
        logger.info("Download cancellation requested")

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the downloaded model (legacy method)"""
        return self.get_current_model_info()

# Convenience functions for easy usage
def download_gemma_model(model_key: Optional[str] = None, progress_callback: Optional[Callable[[float, str], None]] = None) -> bool:
    """
    Convenience function to download Gemma 3n model

    Args:
        model_key: Model key (e.g., 'gemma-3n-E4B', 'gemma-3n-E2B') or None for recommended
        progress_callback: Optional callback function that receives (progress: float, status: str)

    Returns:
        bool: True if download successful, False otherwise
    """
    downloader = HuggingFaceModelDownloader(model_key=model_key)
    return downloader.download_model(progress_callback)

def list_gemma_models() -> Dict[str, Dict]:
    """
    List all available Gemma-3n models

    Returns:
        Dict containing model information
    """
    return HuggingFaceModelDownloader.list_available_models()

if __name__ == "__main__":
    # Test the downloader
    logging.basicConfig(level=logging.INFO)

    def progress_callback(progress: float, status: str):
        print(f"Progress: {progress*100:.1f}% - {status}")

    success = download_gemma_model(progress_callback)
    print(f"Download {'successful' if success else 'failed'}")
