# 🔥 DermatoGemma Multi-Detection System - REAL Gemma 3n E4B Edition

## ⚠️ IMPORTANTE: SEM FALLBACKS - APENAS MODELO REAL

Este sistema foi **COMPLETAMENTE ADAPTADO** para funcionar **EXCLUSIVAMENTE** com o modelo **REAL Gemma 3n E4B** via Ollama. 

**🚫 NENHUM FALLBACK OU SIMULAÇÃO É PERMITIDO**

## 🎯 Características Principais

### ✅ **MODELO REAL OBRIGATÓRIO**
- **Gemma 3n E4B** via Ollama é **OBRIGATÓRIO**
- Sistema **FALHA IMEDIATAMENTE** se o modelo não estiver disponível
- **ZERO TOLERÂNCIA** para fallbacks ou simulações

### ✅ **CAPACIDADES MULTIMODAIS REAIS**
- **Extração de dados visuais** direta de imagens dermatológicas
- **Interpretação e sumarização** automática de características visuais
- **Análise multimodal** integrando imagem + dados clínicos
- **Resultados reais** do modelo de IA avançado

### ✅ **PROCESSAMENTO AVANÇADO DE IMAGEM**
- Resolução otimizada até **1024x1024** pixels
- **Pré-processamento médico** especializado
- **Realce de contraste** para lesões cutâneas
- **Preservação de precisão de cor** para análise dermatológica

### ✅ **CONFIGURAÇÕES OTIMIZADAS**
- Parâmetros baseados na **documentação oficial** do Gemma 3n E4B
- **Temperature: 0.2** para precisão médica máxima
- **Context window: 32K tokens** para análise detalhada
- **Tokens por imagem: 512** para análise visual completa

## 🚀 Instalação e Configuração

### 1. **Setup Automático (Recomendado)**
```bash
# Execute o script de configuração automática
python setup_real_gemma3n_e4b.py
```

### 2. **Setup Manual**
```bash
# 1. Instalar Ollama
# Windows: Baixar de https://ollama.ai/download/windows
# macOS/Linux: curl -fsSL https://ollama.ai/install.sh | sh

# 2. Iniciar serviço Ollama
ollama serve

# 3. Baixar modelo REAL Gemma 3n E4B
ollama pull gemma3n:e4b

# 4. Verificar instalação
ollama list
```

### 3. **Validação do Sistema**
```bash
# Testar sistema REAL (sem fallbacks)
python test_real_gemma3n_e4b.py
```

## 🔬 Uso do Sistema

### **Análise Dermatológica Completa**
```python
from core.gemma_derma_handler import GemmaDermatologyHandlerV2

# Inicializar handler (FALHA se modelo não disponível)
handler = GemmaDermatologyHandlerV2(lazy_load=False)

# Carregar imagem dermatológica
lesion_image = load_image("path/to/lesion.jpg")

# Dados ABCDE (opcional)
abcde_results = {
    'asymmetry': {'score': 0.4},
    'border': {'score': 0.3},
    'color': {'score': 0.5},
    'diameter': {'max_diameter_mm': 6.2},
    'evolution': {'score': 0.2}
}

# Executar análise REAL multimodal
results = handler.analyze_multi_condition(
    lesion_image=lesion_image,
    abcde_results=abcde_results,
    patient_context={'age': 45, 'gender': 'female'}
)

# Acessar resultados REAIS
for condition_id, analysis in results['condition_analyses'].items():
    print(f"{condition_id}: {analysis['probability']:.3f}")
    print(f"Características visuais: {analysis['visual_features']}")
    print(f"Raciocínio clínico: {analysis['reasoning']}")
```

### **Interface Gráfica**
```bash
# Executar interface moderna
python main.py
```

## 📊 Capacidades de Extração Visual

### **Análise Automática de Características**
- **Padrões de Cor**: Distribuição de pigmentação e variações cromáticas
- **Textura e Superfície**: Características morfológicas detalhadas
- **Assimetria**: Análise quantitativa de irregularidades
- **Bordas**: Detecção precisa de irregularidades de contorno
- **Dimensões**: Medições automáticas e análise de tamanho

### **Integração com Critérios ABCDE**
- **A**ssimetria: Análise visual automatizada
- **B**ordas: Detecção de irregularidades
- **C**or: Análise de variações cromáticas
- **D**iâmetro: Medições precisas
- **E**volução: Comparação temporal quando disponível

### **Correlação Clínica Avançada**
- Integração de achados visuais com dados clínicos
- Avaliação de probabilidade baseada em evidências multimodais
- Recomendações clínicas fundamentadas em análise visual

## ⚙️ Configurações Técnicas Avançadas

### **Modelo Gemma 3n E4B**
```python
model_name = "gemma3n:e4b"
context_length = "32K tokens"
effective_parameters = "4B"
multimodal_support = True
```

### **Processamento de Imagem**
```python
supported_resolutions = [224, 256, 384, 448, 512, 576, 640, 768, 896, 1024]
default_resolution = 768  # Otimizado para análise médica
max_resolution = 1024
tokens_per_image = 512
image_quality = 95  # Qualidade JPEG alta
```

### **Configurações do Modelo**
```python
temperature = 0.2  # Conservador para precisão médica
top_p = 0.85
num_predict = 4096  # Análise detalhada
repeat_penalty = 1.15
top_k = 30
num_ctx = 32768  # Contexto completo de 32K
```

## 🧪 Validação e Testes

### **Testes Disponíveis**
1. **`test_real_gemma3n_e4b.py`** - Validação completa do sistema real
2. **`test_gemma3n_e4b_multimodal.py`** - Testes de capacidades multimodais

### **Critérios de Validação**
- ✅ Ollama service funcionando
- ✅ Modelo Gemma 3n E4B disponível
- ✅ Funcionalidade básica do modelo
- ✅ Capacidades multimodais
- ✅ Análise dermatológica completa

## 🚫 O Que Foi REMOVIDO

### **Fallbacks Eliminados**
- ❌ Modo de simulação
- ❌ Análise básica sem IA
- ❌ Respostas mock
- ❌ Processamento offline
- ❌ Análise heurística

### **Comportamento Atual**
- 🔥 **FALHA IMEDIATA** se Ollama não estiver rodando
- 🔥 **FALHA IMEDIATA** se modelo não estiver disponível
- 🔥 **FALHA IMEDIATA** se análise multimodal falhar
- 🔥 **ZERO TOLERÂNCIA** para degradação de funcionalidade

## 📈 Melhorias Implementadas

### **Baseadas na Documentação Oficial**
1. **Processamento de Imagem Avançado**
   - Normalização ImageNet para E4B
   - Realce de contraste médico
   - Preservação de proporções
   - Otimização de qualidade

2. **Configurações Otimizadas**
   - Parâmetros baseados nas docs oficiais
   - Timeouts adaptativos
   - Configuração dinâmica baseada na imagem
   - Gestão eficiente de memória

3. **Análise Médica Especializada**
   - Extração de características dermatológicas
   - Análise de tom de pele
   - Cálculo de contraste de lesão
   - Avaliação de complexidade de textura

## 🎯 Resultados Esperados

### **Precisão Diagnóstica**
- **Análise visual direta** das lesões cutâneas
- **Extração automática** de características relevantes
- **Correlação multimodal** entre imagem e dados clínicos
- **Insights visuais detalhados** para suporte diagnóstico

### **Performance**
- **Processamento otimizado** para modelo E4B
- **Análise em tempo real** de imagens médicas
- **Configuração adaptativa** baseada na complexidade
- **Gestão eficiente** de recursos computacionais

## 🔧 Solução de Problemas

### **Erro: "Cannot connect to Ollama"**
```bash
# Verificar se Ollama está rodando
ollama serve

# Verificar status
curl http://localhost:11434/api/tags
```

### **Erro: "Model not available"**
```bash
# Baixar modelo
ollama pull gemma3n:e4b

# Verificar modelos disponíveis
ollama list
```

### **Erro: "Multimodal analysis failed"**
- Verificar formato da imagem (JPEG, PNG, WEBP)
- Verificar tamanho da imagem (máximo 1024x1024)
- Verificar qualidade da imagem

## 📚 Documentação Técnica

- **Gemma 3n Docs**: https://ai.google.dev/gemma/docs/gemma-3n
- **Ollama Gemma 3n E4B**: https://ollama.com/library/gemma3n:e4b
- **Hugging Face Model**: https://huggingface.co/google/gemma-3n-E4B
- **Vision Guide**: https://ai.google.dev/gemma/docs/core/huggingface_inference#vision

---

**🏥 DermatoGemma Multi-Detection System - REAL Gemma 3n E4B Edition**  
*Análise dermatológica REAL com IA multimodal avançada - SEM FALLBACKS*
