{"system": {"version": "2.0-Olla<PERSON>", "setup_date": "2025-07-26T00:00:00.000000", "platform": "Windows", "gpu_available": false, "ai_backend": "ollama", "model_name": "gemma3n:e2b"}, "ollama": {"url": "http://localhost:11434", "model": "gemma3n:e2b", "timeouts": {"connection_check": 3600, "model_test": 1800, "analysis": 3600, "simple_query": 900}, "temperature": 0.3, "max_tokens": 2048}, "analysis": {"enable_abcde_analysis": true, "enable_ai_analysis": true, "enable_temporal_analysis": true, "confidence_threshold": 0.3, "max_lesions_per_image": 10, "target_diseases": ["actinic_keratoses", "basal_cell_carcinoma", "benign_keratosis_like_lesions", "chickenpox", "cowpox", "dermatofibroma", "healthy", "hfmd", "measles", "melanocytic_nevi", "melanoma", "monkeypox", "squamous_cell_carcinoma", "vascular_lesions"]}, "ui": {"theme": "medical_professional", "auto_save_results": true, "show_confidence_scores": true, "enable_animations": true}, "privacy": {"local_processing_only": true, "encrypt_patient_data": true, "auto_delete_temp_files": true}}