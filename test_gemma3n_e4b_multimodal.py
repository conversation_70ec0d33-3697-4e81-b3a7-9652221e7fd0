#!/usr/bin/env python3
"""
Test script for DermatoGemma Multi-Detection System with Gemma 3n E4B
Validates multimodal image analysis capabilities and visual data extraction

This script tests:
- Gemma 3n E4B model initialization and configuration
- Multimodal image processing and analysis
- Visual data extraction and interpretation
- Enhanced dermatological assessment capabilities
- Error handling and fallback mechanisms
"""

import sys
import os
import logging
import time
import numpy as np
from PIL import Image
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

# Import core modules
from core.gemma_derma_handler import GemmaDermatologyHandlerV2
from core.multi_condition_engine import MultiConditionAnalysisEngine
from core.abcde_analyzer import AdvancedABCDEAnalyzer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class Gemma3nE4BTestSuite:
    """Test suite for Gemma 3n E4B multimodal capabilities"""
    
    def __init__(self):
        self.test_results = {}
        self.test_images_dir = Path("test_images")
        
    def run_all_tests(self):
        """Run comprehensive test suite"""
        logger.info("🧪 Starting Gemma 3n E4B Multimodal Test Suite")
        logger.info("=" * 60)
        
        tests = [
            ("Model Initialization", self.test_model_initialization),
            ("Multimodal Support", self.test_multimodal_support),
            ("Image Processing", self.test_image_processing),
            ("Visual Data Extraction", self.test_visual_data_extraction),
            ("Multimodal Analysis", self.test_multimodal_analysis),
            ("Error Handling", self.test_error_handling),
            ("Performance Optimization", self.test_performance_optimization)
        ]
        
        for test_name, test_func in tests:
            logger.info(f"\n🔬 Running test: {test_name}")
            try:
                result = test_func()
                self.test_results[test_name] = result
                status = "✅ PASSED" if result['success'] else "❌ FAILED"
                logger.info(f"{status}: {test_name}")
                if not result['success']:
                    logger.error(f"Error: {result.get('error', 'Unknown error')}")
            except Exception as e:
                logger.error(f"❌ FAILED: {test_name} - {e}")
                self.test_results[test_name] = {'success': False, 'error': str(e)}
        
        self.print_summary()
    
    def test_model_initialization(self):
        """Test Gemma 3n E4B model initialization"""
        try:
            handler = GemmaDermatologyHandlerV2(lazy_load=True)
            
            # Check model configuration
            assert handler.model_name == "gemma3n:e4b", f"Expected gemma3n:e4b, got {handler.model_name}"
            assert handler._supports_vision == True, "Vision support should be enabled"
            assert 'image_config' in handler.__dict__, "Image configuration missing"
            
            # Check image configuration
            img_config = handler.image_config
            assert 512 in img_config['supported_resolutions'], "512x512 resolution should be supported"
            assert img_config['tokens_per_image'] == 256, "Expected 256 tokens per image"
            
            # Check performance configuration
            assert hasattr(handler, 'performance_config'), "Performance configuration missing"
            
            return {
                'success': True,
                'model_name': handler.model_name,
                'vision_support': handler._supports_vision,
                'image_config': img_config
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_multimodal_support(self):
        """Test multimodal support verification"""
        try:
            handler = GemmaDermatologyHandlerV2(lazy_load=True)
            
            # Test image preparation method
            test_image = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
            pil_image = Image.fromarray(test_image)
            
            # Test image preparation
            image_b64 = handler._prepare_image_for_gemma3n(pil_image)
            assert image_b64 is not None, "Image preparation failed"
            assert isinstance(image_b64, str), "Image should be base64 string"
            
            # Test image enhancement
            enhanced_image = handler._enhance_dermatological_image(pil_image)
            assert isinstance(enhanced_image, Image.Image), "Enhanced image should be PIL Image"
            
            # Test metadata extraction
            metadata = handler._extract_image_metadata(pil_image)
            assert 'dimensions' in metadata, "Dimensions missing from metadata"
            assert 'color_analysis' in metadata, "Color analysis missing from metadata"
            
            return {
                'success': True,
                'image_prepared': image_b64 is not None,
                'metadata_extracted': len(metadata) > 0,
                'enhancement_applied': enhanced_image is not None
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_image_processing(self):
        """Test advanced image processing capabilities"""
        try:
            handler = GemmaDermatologyHandlerV2(lazy_load=True)
            
            # Create test image with known characteristics
            test_image = np.random.randint(0, 255, (400, 600, 3), dtype=np.uint8)
            pil_image = Image.fromarray(test_image)
            
            # Test image metadata extraction
            metadata = handler._extract_image_metadata(pil_image)
            
            # Verify metadata structure
            required_keys = ['dimensions', 'color_analysis', 'quality_metrics']
            for key in required_keys:
                assert key in metadata, f"Missing metadata key: {key}"
            
            # Test dimension analysis
            dims = metadata['dimensions']
            assert dims['width'] == 600, f"Expected width 600, got {dims['width']}"
            assert dims['height'] == 400, f"Expected height 400, got {dims['height']}"
            
            # Test color analysis
            color_analysis = metadata['color_analysis']
            assert 'mean_rgb' in color_analysis, "Mean RGB missing"
            assert 'std_rgb' in color_analysis, "RGB std missing"
            assert len(color_analysis['mean_rgb']) == 3, "Mean RGB should have 3 values"
            
            return {
                'success': True,
                'metadata_complete': True,
                'dimensions_correct': True,
                'color_analysis_present': True
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_visual_data_extraction(self):
        """Test visual data extraction capabilities"""
        try:
            handler = GemmaDermatologyHandlerV2(lazy_load=True)
            
            # Test enhanced context preparation
            test_image = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
            
            # Mock ABCDE results
            abcde_results = {
                'asymmetry': {'score': 0.3},
                'border': {'score': 0.4},
                'color': {'score': 0.5},
                'diameter': {'max_diameter_mm': 6.2},
                'evolution': {'score': 0.2}
            }
            
            # Test enhanced context preparation
            context = handler._prepare_enhanced_analysis_context(
                test_image, abcde_results, None, None
            )
            
            # Verify enhanced context
            assert 'enhanced_image_metadata' in context, "Enhanced metadata missing"
            assert 'image_ready_for_analysis' in context, "Image readiness flag missing"
            assert 'visual_analysis_enabled' in context, "Visual analysis flag missing"
            
            return {
                'success': True,
                'enhanced_context': True,
                'image_metadata_present': 'enhanced_image_metadata' in context,
                'visual_analysis_ready': context.get('visual_analysis_enabled', False)
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_multimodal_analysis(self):
        """Test multimodal analysis workflow"""
        try:
            # This test requires Ollama to be running, so we'll test the preparation
            handler = GemmaDermatologyHandlerV2(lazy_load=True)
            
            # Test prompt creation for multimodal analysis
            condition_info = {
                'name': 'Melanoma',
                'type': 'malignant',
                'urgency': 'high',
                'key_features': ['asymmetry', 'irregular borders', 'color variation']
            }
            
            context = {
                'enhanced_image_metadata': {
                    'quality_metrics': {'sharpness': 150, 'noise_level': 25}
                }
            }
            
            # Test prompt creation
            prompt = handler._create_condition_specific_prompt('melanoma', condition_info, context)
            
            # Verify prompt contains multimodal instructions
            assert 'multimodal' in prompt.lower(), "Prompt should mention multimodal analysis"
            assert 'visual' in prompt.lower(), "Prompt should mention visual analysis"
            assert 'image' in prompt.lower(), "Prompt should mention image analysis"
            
            # Test configuration optimization
            optimized_config = handler._optimize_model_config_for_image(context['enhanced_image_metadata'])
            assert isinstance(optimized_config, dict), "Optimized config should be dict"
            assert 'temperature' in optimized_config, "Temperature should be in config"
            
            return {
                'success': True,
                'prompt_generated': True,
                'multimodal_instructions': 'multimodal' in prompt.lower(),
                'config_optimized': True
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_error_handling(self):
        """Test error handling and fallback mechanisms"""
        try:
            handler = GemmaDermatologyHandlerV2(lazy_load=True)
            
            # Test with invalid image
            invalid_result = handler._prepare_image_for_gemma3n("invalid_input")
            assert invalid_result is None, "Should return None for invalid input"
            
            # Test multimodal error result creation
            context = {'image_ready_for_analysis': True, 'visual_analysis_enabled': True}
            error_result = handler._create_multimodal_error_result("Test error", context)
            
            assert error_result['success'] == False, "Error result should indicate failure"
            assert 'multimodal' in error_result['error_type'], "Should be multimodal error type"
            assert error_result['fallback_available'] == True, "Fallback should be available"
            
            return {
                'success': True,
                'invalid_input_handled': True,
                'error_result_structure': True,
                'fallback_available': True
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_performance_optimization(self):
        """Test performance optimization features"""
        try:
            handler = GemmaDermatologyHandlerV2(lazy_load=True)
            
            # Test adaptive timeout calculation
            metadata = {
                'dimensions': {'width': 1024, 'height': 1024},
                'quality_metrics': {'sharpness': 50, 'noise_level': 80}
            }
            
            adaptive_timeout = handler._calculate_adaptive_timeout(metadata)
            base_timeout = handler.timeouts['multimodal_analysis']
            
            # Should increase timeout for large, low-quality image
            assert adaptive_timeout >= base_timeout, "Timeout should be increased for complex image"
            
            # Test configuration optimization
            optimized_config = handler._optimize_model_config_for_image(metadata)
            
            # Should use conservative settings for low quality
            assert optimized_config['temperature'] <= 0.3, "Should use conservative temperature for low quality"
            
            return {
                'success': True,
                'adaptive_timeout': adaptive_timeout > base_timeout,
                'config_optimization': True,
                'performance_tuning': True
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def print_summary(self):
        """Print test results summary"""
        logger.info("\n" + "=" * 60)
        logger.info("🧪 GEMMA 3N E4B MULTIMODAL TEST RESULTS")
        logger.info("=" * 60)
        
        passed = sum(1 for result in self.test_results.values() if result.get('success', False))
        total = len(self.test_results)
        
        logger.info(f"Tests Passed: {passed}/{total}")
        logger.info(f"Success Rate: {(passed/total)*100:.1f}%")
        
        logger.info("\nDetailed Results:")
        for test_name, result in self.test_results.items():
            status = "✅ PASSED" if result.get('success', False) else "❌ FAILED"
            logger.info(f"  {status} {test_name}")
            if not result.get('success', False) and 'error' in result:
                logger.info(f"    Error: {result['error']}")
        
        if passed == total:
            logger.info("\n🎉 All tests passed! Gemma 3n E4B multimodal system is ready.")
        else:
            logger.info(f"\n⚠️  {total - passed} test(s) failed. Please review the errors above.")

def main():
    """Main test execution"""
    print("🏥 DermatoGemma Multi-Detection System - Gemma 3n E4B Test Suite")
    print("Testing multimodal image analysis capabilities...")
    print()
    
    test_suite = Gemma3nE4BTestSuite()
    test_suite.run_all_tests()

if __name__ == "__main__":
    main()
