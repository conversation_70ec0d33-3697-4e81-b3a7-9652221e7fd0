"""
🏥 DermatoGemma Multi-Detection System v2.0
Revolutionary Multi-Condition User Interface

This module provides an advanced, medical-grade user interface for simultaneous
analysis of multiple dermatological conditions with professional visualization.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import numpy as np
import cv2
from PIL import Image, ImageTk, ImageDraw, ImageFont
from typing import Dict, List, Tuple, Optional, Any
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import seaborn as sns
import logging
from datetime import datetime
import json
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RevolutionaryMultiConditionUI:
    """
    🎨 Revolutionary Multi-Condition Dermatological Interface
    
    Features:
    - Professional medical-grade design
    - Real-time multi-condition analysis visualization
    - Interactive lesion mapping and annotation
    - Advanced results dashboard with charts
    - Clinical report generation
    - Temporal analysis comparison
    - Risk stratification visualization
    """
    
    def __init__(self, analysis_engine, lazy_load=False):
        self.analysis_engine = analysis_engine
        self.lazy_load = lazy_load
        self.root = None
        self.current_image = None
        self.current_results = None
        self.analysis_history = []
        
        # UI Configuration
        self.colors = {
            'primary': '#2C3E50',      # Dark blue-gray
            'secondary': '#3498DB',     # Blue
            'success': '#27AE60',       # Green
            'warning': '#F39C12',       # Orange
            'danger': '#E74C3C',        # Red
            'light': '#ECF0F1',         # Light gray
            'dark': '#34495E',          # Dark gray
            'white': '#FFFFFF',
            'medical_blue': '#4A90E2',
            'medical_green': '#7ED321',
            'medical_red': '#D0021B',
            'medical_orange': '#F5A623'
        }
        
        self.fonts = {
            'title': ('Segoe UI', 16, 'bold'),
            'subtitle': ('Segoe UI', 12, 'bold'),
            'body': ('Segoe UI', 10),
            'small': ('Segoe UI', 8),
            'medical': ('Segoe UI', 11, 'bold')
        }
        
        # Condition display configuration
        self.condition_config = {
            'melanoma': {'color': '#D0021B', 'priority': 1, 'icon': '⚠️'},
            'basal_cell_carcinoma': {'color': '#F5A623', 'priority': 2, 'icon': '🔶'},
            'squamous_cell_carcinoma': {'color': '#F5A623', 'priority': 3, 'icon': '🔸'},
            'atypical_nevus': {'color': '#BD10E0', 'priority': 4, 'icon': '🟣'},
            'actinic_keratosis': {'color': '#B8E986', 'priority': 5, 'icon': '🟢'},
            'seborrheic_keratosis': {'color': '#50E3C2', 'priority': 6, 'icon': '🔵'},
            'dermatofibroma': {'color': '#4A90E2', 'priority': 7, 'icon': '🔷'},
            'hemangioma': {'color': '#9013FE', 'priority': 8, 'icon': '🟪'}
        }
        
        logger.info("🎨 Revolutionary Multi-Condition UI initialized")
    
    def create_main_interface(self, root: tk.Tk):
        """Create the main revolutionary interface"""
        self.root = root
        
        # Configure main window
        self.root.title("🏥 DermatoGemma Multi-Detection System v2.0")
        self.root.geometry("1400x900")
        self.root.configure(bg=self.colors['light'])
        
        # Create main layout
        self._create_header()
        self._create_main_content()
        self._create_status_bar()
        
        logger.info("✅ Revolutionary interface created")
    
    def _create_header(self):
        """Create professional medical header"""
        header_frame = tk.Frame(self.root, bg=self.colors['primary'], height=80)
        header_frame.pack(fill='x', padx=0, pady=0)
        header_frame.pack_propagate(False)
        
        # Title and logo area
        title_frame = tk.Frame(header_frame, bg=self.colors['primary'])
        title_frame.pack(side='left', padx=20, pady=10)
        
        title_label = tk.Label(
            title_frame,
            text="🏥 DermatoGemma Multi-Detection System",
            font=self.fonts['title'],
            fg=self.colors['white'],
            bg=self.colors['primary']
        )
        title_label.pack(anchor='w')
        
        subtitle_label = tk.Label(
            title_frame,
            text="Revolutionary AI-Powered Dermatological Screening",
            font=self.fonts['body'],
            fg=self.colors['light'],
            bg=self.colors['primary']
        )
        subtitle_label.pack(anchor='w')
        
        # Action buttons
        button_frame = tk.Frame(header_frame, bg=self.colors['primary'])
        button_frame.pack(side='right', padx=20, pady=10)
        
        load_btn = tk.Button(
            button_frame,
            text="📁 Load Image",
            font=self.fonts['body'],
            bg=self.colors['secondary'],
            fg=self.colors['white'],
            relief='flat',
            padx=20,
            pady=8,
            command=self._load_image
        )
        load_btn.pack(side='left', padx=5)
        
        analyze_btn = tk.Button(
            button_frame,
            text="🔬 Analyze Multi-Conditions",
            font=self.fonts['body'],
            bg=self.colors['success'],
            fg=self.colors['white'],
            relief='flat',
            padx=20,
            pady=8,
            command=self._analyze_image
        )
        analyze_btn.pack(side='left', padx=5)
        
        report_btn = tk.Button(
            button_frame,
            text="📋 Generate Report",
            font=self.fonts['body'],
            bg=self.colors['medical_blue'],
            fg=self.colors['white'],
            relief='flat',
            padx=20,
            pady=8,
            command=self._generate_report
        )
        report_btn.pack(side='left', padx=5)
    
    def _create_main_content(self):
        """Create main content area with tabs"""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Configure notebook style
        style = ttk.Style()
        style.configure('TNotebook.Tab', padding=[20, 10])
        
        # Create tabs
        self._create_analysis_tab()
        self._create_results_dashboard_tab()
        self._create_abcde_analysis_tab()
        self._create_comparison_tab()
        self._create_history_tab()
    
    def _create_analysis_tab(self):
        """Create main analysis tab"""
        analysis_frame = ttk.Frame(self.notebook)
        self.notebook.add(analysis_frame, text="🔬 Multi-Condition Analysis")
        
        # Create main layout
        main_paned = tk.PanedWindow(analysis_frame, orient='horizontal', bg=self.colors['light'])
        main_paned.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Left panel - Image display
        left_frame = tk.Frame(main_paned, bg=self.colors['white'], relief='raised', bd=1)
        main_paned.add(left_frame, width=600)
        
        # Image display area
        image_label = tk.Label(
            left_frame,
            text="🏥 DermatoGemma Multi-Detection\n\n📁 Load an image to begin analysis\n\n🔬 Advanced AI will analyze for:\n• Melanoma\n• Basal Cell Carcinoma\n• Squamous Cell Carcinoma\n• Atypical Nevus\n• Seborrheic Keratosis\n• Actinic Keratosis\n• Dermatofibroma\n• Hemangioma",
            font=self.fonts['subtitle'],
            fg=self.colors['dark'],
            bg=self.colors['white'],
            justify='center'
        )
        image_label.pack(expand=True, fill='both', padx=20, pady=20)
        
        self.image_display = image_label
        
        # Right panel - Analysis controls and quick results
        right_frame = tk.Frame(main_paned, bg=self.colors['light'], relief='raised', bd=1)
        main_paned.add(right_frame, width=400)
        
        # Analysis controls
        controls_frame = tk.LabelFrame(
            right_frame,
            text="🎛️ Analysis Controls",
            font=self.fonts['subtitle'],
            bg=self.colors['light'],
            fg=self.colors['dark']
        )
        controls_frame.pack(fill='x', padx=10, pady=5)
        
        # Patient information
        patient_frame = tk.LabelFrame(
            controls_frame,
            text="👤 Patient Information",
            font=self.fonts['body'],
            bg=self.colors['light']
        )
        patient_frame.pack(fill='x', padx=5, pady=5)
        
        # Age
        age_frame = tk.Frame(patient_frame, bg=self.colors['light'])
        age_frame.pack(fill='x', padx=5, pady=2)
        tk.Label(age_frame, text="Age:", font=self.fonts['body'], bg=self.colors['light']).pack(side='left')
        self.age_var = tk.StringVar()
        age_entry = tk.Entry(age_frame, textvariable=self.age_var, width=10)
        age_entry.pack(side='right')
        
        # Gender
        gender_frame = tk.Frame(patient_frame, bg=self.colors['light'])
        gender_frame.pack(fill='x', padx=5, pady=2)
        tk.Label(gender_frame, text="Gender:", font=self.fonts['body'], bg=self.colors['light']).pack(side='left')
        self.gender_var = tk.StringVar(value="Not specified")
        gender_combo = ttk.Combobox(gender_frame, textvariable=self.gender_var, 
                                   values=["Male", "Female", "Not specified"], width=12)
        gender_combo.pack(side='right')
        
        # Skin type
        skin_frame = tk.Frame(patient_frame, bg=self.colors['light'])
        skin_frame.pack(fill='x', padx=5, pady=2)
        tk.Label(skin_frame, text="Skin Type:", font=self.fonts['body'], bg=self.colors['light']).pack(side='left')
        self.skin_type_var = tk.StringVar(value="Type II")
        skin_combo = ttk.Combobox(skin_frame, textvariable=self.skin_type_var,
                                 values=["Type I", "Type II", "Type III", "Type IV", "Type V", "Type VI"], width=10)
        skin_combo.pack(side='right')
        
        # Analysis options
        options_frame = tk.LabelFrame(
            controls_frame,
            text="⚙️ Analysis Options",
            font=self.fonts['body'],
            bg=self.colors['light']
        )
        options_frame.pack(fill='x', padx=5, pady=5)
        
        self.enable_abcde_var = tk.BooleanVar(value=True)
        abcde_check = tk.Checkbutton(
            options_frame,
            text="Enable ABCDE Analysis",
            variable=self.enable_abcde_var,
            font=self.fonts['body'],
            bg=self.colors['light']
        )
        abcde_check.pack(anchor='w', padx=5, pady=2)
        
        self.enable_ai_var = tk.BooleanVar(value=True)
        ai_check = tk.Checkbutton(
            options_frame,
            text="Enable AI Multi-Condition Analysis",
            variable=self.enable_ai_var,
            font=self.fonts['body'],
            bg=self.colors['light']
        )
        ai_check.pack(anchor='w', padx=5, pady=2)
        
        self.enable_temporal_var = tk.BooleanVar(value=False)
        temporal_check = tk.Checkbutton(
            options_frame,
            text="Enable Temporal Analysis",
            variable=self.enable_temporal_var,
            font=self.fonts['body'],
            bg=self.colors['light']
        )
        temporal_check.pack(anchor='w', padx=5, pady=2)
        
        # Quick results area
        quick_results_frame = tk.LabelFrame(
            right_frame,
            text="⚡ Quick Results",
            font=self.fonts['subtitle'],
            bg=self.colors['light'],
            fg=self.colors['dark']
        )
        quick_results_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        self.quick_results_text = tk.Text(
            quick_results_frame,
            font=self.fonts['body'],
            bg=self.colors['white'],
            fg=self.colors['dark'],
            wrap='word',
            state='disabled'
        )
        self.quick_results_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Add scrollbar
        scrollbar = tk.Scrollbar(quick_results_frame, command=self.quick_results_text.yview)
        scrollbar.pack(side='right', fill='y')
        self.quick_results_text.config(yscrollcommand=scrollbar.set)
    
    def _create_results_dashboard_tab(self):
        """Create comprehensive results dashboard"""
        dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="📊 Results Dashboard")
        
        # Create dashboard layout
        dashboard_paned = tk.PanedWindow(dashboard_frame, orient='vertical', bg=self.colors['light'])
        dashboard_paned.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Top section - Summary cards
        summary_frame = tk.Frame(dashboard_paned, bg=self.colors['light'], height=200)
        dashboard_paned.add(summary_frame)
        
        # Create summary cards
        self._create_summary_cards(summary_frame)
        
        # Bottom section - Charts and detailed results
        charts_frame = tk.Frame(dashboard_paned, bg=self.colors['light'])
        dashboard_paned.add(charts_frame)
        
        # Create charts area
        self._create_charts_area(charts_frame)
    
    def _create_summary_cards(self, parent):
        """Create summary cards for key metrics"""
        cards_frame = tk.Frame(parent, bg=self.colors['light'])
        cards_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Risk level card
        risk_card = tk.Frame(cards_frame, bg=self.colors['white'], relief='raised', bd=2)
        risk_card.pack(side='left', fill='both', expand=True, padx=5)
        
        tk.Label(
            risk_card,
            text="⚠️ Overall Risk Level",
            font=self.fonts['subtitle'],
            bg=self.colors['white'],
            fg=self.colors['dark']
        ).pack(pady=5)
        
        self.risk_level_label = tk.Label(
            risk_card,
            text="Not Analyzed",
            font=('Segoe UI', 24, 'bold'),
            bg=self.colors['white'],
            fg=self.colors['dark']
        )
        self.risk_level_label.pack(pady=10)
        
        # Lesions detected card
        lesions_card = tk.Frame(cards_frame, bg=self.colors['white'], relief='raised', bd=2)
        lesions_card.pack(side='left', fill='both', expand=True, padx=5)
        
        tk.Label(
            lesions_card,
            text="🔍 Lesions Detected",
            font=self.fonts['subtitle'],
            bg=self.colors['white'],
            fg=self.colors['dark']
        ).pack(pady=5)
        
        self.lesions_count_label = tk.Label(
            lesions_card,
            text="0",
            font=('Segoe UI', 24, 'bold'),
            bg=self.colors['white'],
            fg=self.colors['secondary']
        )
        self.lesions_count_label.pack(pady=10)
        
        # Top condition card
        condition_card = tk.Frame(cards_frame, bg=self.colors['white'], relief='raised', bd=2)
        condition_card.pack(side='left', fill='both', expand=True, padx=5)
        
        tk.Label(
            condition_card,
            text="🎯 Most Likely Condition",
            font=self.fonts['subtitle'],
            bg=self.colors['white'],
            fg=self.colors['dark']
        ).pack(pady=5)
        
        self.top_condition_label = tk.Label(
            condition_card,
            text="None",
            font=self.fonts['medical'],
            bg=self.colors['white'],
            fg=self.colors['dark'],
            wraplength=150
        )
        self.top_condition_label.pack(pady=10)
        
        # Confidence card
        confidence_card = tk.Frame(cards_frame, bg=self.colors['white'], relief='raised', bd=2)
        confidence_card.pack(side='left', fill='both', expand=True, padx=5)
        
        tk.Label(
            confidence_card,
            text="📈 Analysis Confidence",
            font=self.fonts['subtitle'],
            bg=self.colors['white'],
            fg=self.colors['dark']
        ).pack(pady=5)
        
        self.confidence_label = tk.Label(
            confidence_card,
            text="0%",
            font=('Segoe UI', 24, 'bold'),
            bg=self.colors['white'],
            fg=self.colors['success']
        )
        self.confidence_label.pack(pady=10)
    
    def _create_charts_area(self, parent):
        """Create area for charts and visualizations"""
        charts_paned = tk.PanedWindow(parent, orient='horizontal', bg=self.colors['light'])
        charts_paned.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Left chart - Condition probabilities
        left_chart_frame = tk.Frame(charts_paned, bg=self.colors['white'], relief='raised', bd=1)
        charts_paned.add(left_chart_frame, width=500)
        
        tk.Label(
            left_chart_frame,
            text="📊 Condition Probabilities",
            font=self.fonts['subtitle'],
            bg=self.colors['white'],
            fg=self.colors['dark']
        ).pack(pady=5)
        
        # Placeholder for matplotlib chart
        self.condition_chart_frame = tk.Frame(left_chart_frame, bg=self.colors['white'])
        self.condition_chart_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Right area - Detailed results
        right_results_frame = tk.Frame(charts_paned, bg=self.colors['white'], relief='raised', bd=1)
        charts_paned.add(right_results_frame, width=400)
        
        tk.Label(
            right_results_frame,
            text="📋 Detailed Analysis Results",
            font=self.fonts['subtitle'],
            bg=self.colors['white'],
            fg=self.colors['dark']
        ).pack(pady=5)
        
        self.detailed_results_text = tk.Text(
            right_results_frame,
            font=self.fonts['small'],
            bg=self.colors['white'],
            fg=self.colors['dark'],
            wrap='word',
            state='disabled'
        )
        self.detailed_results_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Add scrollbar
        detailed_scrollbar = tk.Scrollbar(right_results_frame, command=self.detailed_results_text.yview)
        detailed_scrollbar.pack(side='right', fill='y')
        self.detailed_results_text.config(yscrollcommand=detailed_scrollbar.set)

    def _create_abcde_analysis_tab(self):
        """Create ABCDE analysis tab"""
        abcde_frame = ttk.Frame(self.notebook)
        self.notebook.add(abcde_frame, text="ABCDE Analysis")

        # Placeholder for ABCDE analysis
        placeholder_label = tk.Label(
            abcde_frame,
            text="ABCDE Analysis Tab\n\nDetailed melanoma analysis will be displayed here.",
            font=self.fonts['subtitle'],
            bg=self.colors['white'],
            fg=self.colors['dark']
        )
        placeholder_label.pack(expand=True, fill='both', padx=20, pady=20)

    def _create_comparison_tab(self):
        """Create comparison tab"""
        comparison_frame = ttk.Frame(self.notebook)
        self.notebook.add(comparison_frame, text="Comparison")

        # Placeholder for comparison
        placeholder_label = tk.Label(
            comparison_frame,
            text="Comparison Tab\n\nSide-by-side condition comparison will be displayed here.",
            font=self.fonts['subtitle'],
            bg=self.colors['white'],
            fg=self.colors['dark']
        )
        placeholder_label.pack(expand=True, fill='both', padx=20, pady=20)

    def _create_history_tab(self):
        """Create history tab"""
        history_frame = ttk.Frame(self.notebook)
        self.notebook.add(history_frame, text="History")

        # Placeholder for history
        placeholder_label = tk.Label(
            history_frame,
            text="Analysis History Tab\n\nPrevious analyses will be displayed here.",
            font=self.fonts['subtitle'],
            bg=self.colors['white'],
            fg=self.colors['dark']
        )
        placeholder_label.pack(expand=True, fill='both', padx=20, pady=20)

    def _load_image(self):
        """Load image for analysis"""
        try:
            from tkinter import filedialog

            file_path = filedialog.askopenfilename(
                title="Select Dermatological Image",
                filetypes=[
                    ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff"),
                    ("All files", "*.*")
                ]
            )

            if file_path:
                # Load and display image
                self.current_image = file_path
                self._display_image(file_path)

                # Update quick results
                self._update_quick_results("Image loaded successfully. Ready for analysis.")

                logger.info(f"Image loaded: {file_path}")

        except Exception as e:
            logger.error(f"Failed to load image: {e}")
            messagebox.showerror("Error", f"Failed to load image: {str(e)}")

    def _display_image(self, image_path):
        """Display loaded image"""
        try:
            from PIL import Image, ImageTk

            # Load image
            pil_image = Image.open(image_path)

            # Resize image to fit display
            display_size = (400, 400)
            pil_image.thumbnail(display_size, Image.Resampling.LANCZOS)

            # Convert to PhotoImage
            photo = ImageTk.PhotoImage(pil_image)

            # Update image display
            self.image_display.configure(image=photo, text="")
            self.image_display.image = photo  # Keep a reference

        except Exception as e:
            logger.error(f"Failed to display image: {e}")
            self.image_display.configure(text=f"Failed to display image: {str(e)}")

    def _analyze_image(self):
        """Analyze loaded image"""
        try:
            if not hasattr(self, 'current_image') or not self.current_image:
                messagebox.showwarning("Warning", "Please load an image first.")
                return

            # Update quick results
            self._update_quick_results("Analysis in progress...")

            # Get patient information
            patient_context = {
                'age': self.age_var.get(),
                'gender': self.gender_var.get(),
                'skin_type': self.skin_type_var.get()
            }

            # Get analysis options
            analysis_options = {
                'enable_abcde_analysis': self.enable_abcde_var.get(),
                'enable_ai_analysis': self.enable_ai_var.get(),
                'enable_temporal_analysis': self.enable_temporal_var.get()
            }

            # Perform analysis (demo mode)
            if self.analysis_engine is None:
                # Demo analysis
                self._perform_demo_analysis()
            else:
                # Real analysis
                self._perform_real_analysis(patient_context, analysis_options)

        except Exception as e:
            logger.error(f"Analysis failed: {e}")
            messagebox.showerror("Error", f"Analysis failed: {str(e)}")
            self._update_quick_results(f"Analysis failed: {str(e)}")

    def _perform_demo_analysis(self):
        """Perform demo analysis with sample results"""
        import time
        import random

        # Simulate analysis time
        self.root.update()
        time.sleep(2)

        # Generate demo results
        conditions = list(self.condition_config.keys())
        selected_condition = random.choice(conditions)
        confidence = random.uniform(0.6, 0.95)

        demo_results = f"""Demo Analysis Results:

Total Lesions Detected: {random.randint(1, 3)}
Risk Level: {'High' if confidence > 0.8 else 'Medium' if confidence > 0.6 else 'Low'}
Most Likely Condition: {selected_condition.replace('_', ' ').title()}
Confidence: {confidence:.1%}

ABCDE Analysis:
- Asymmetry: {random.uniform(0.2, 0.8):.2f}
- Border: {random.uniform(0.1, 0.7):.2f}
- Color: {random.uniform(0.3, 0.9):.2f}
- Diameter: {random.uniform(3, 12):.1f}mm
- Evolution: Not available

Recommendations:
- {'Immediate dermatologist consultation' if confidence > 0.8 else 'Monitor closely and consider professional evaluation'}
- Document findings for medical records
- Follow up in {'1-2 weeks' if confidence > 0.8 else '3-6 months'}

Note: This is a demonstration. Use the full system for real analysis."""

        self._update_quick_results(demo_results)
        self._update_dashboard_demo(selected_condition, confidence)

        logger.info("Demo analysis completed")

    def _perform_real_analysis(self, patient_context, analysis_options):
        """Perform real analysis using the analysis engine"""
        try:
            # This would call the real analysis engine
            results = self.analysis_engine.analyze_comprehensive(
                self.current_image,
                patient_context=patient_context,
                analysis_options=analysis_options
            )

            # Update interface with real results
            self._update_results_from_analysis(results)

        except Exception as e:
            logger.error(f"Real analysis failed: {e}")
            # Fallback to demo
            self._perform_demo_analysis()

    def _update_quick_results(self, text):
        """Update quick results text area"""
        try:
            self.quick_results_text.config(state='normal')
            self.quick_results_text.delete(1.0, tk.END)
            self.quick_results_text.insert(1.0, text)
            self.quick_results_text.config(state='disabled')
        except Exception as e:
            logger.error(f"Failed to update quick results: {e}")

    def _update_dashboard_demo(self, condition, confidence):
        """Update dashboard with demo results"""
        try:
            # Update summary cards
            self.risk_level_label.config(
                text="MEDIUM" if confidence > 0.7 else "LOW",
                fg=self.colors['warning'] if confidence > 0.7 else self.colors['success']
            )

            self.lesions_count_label.config(text="2")

            self.top_condition_label.config(
                text=condition.replace('_', ' ').title()
            )

            self.confidence_label.config(text=f"{confidence:.0%}")

        except Exception as e:
            logger.error(f"Failed to update dashboard: {e}")

    def _generate_report(self):
        """Generate medical report"""
        try:
            if not hasattr(self, 'current_image') or not self.current_image:
                messagebox.showwarning("Warning", "Please analyze an image first.")
                return

            # Demo report generation
            from tkinter import filedialog

            file_path = filedialog.asksaveasfilename(
                title="Save Report",
                defaultextension=".txt",
                filetypes=[
                    ("Text files", "*.txt"),
                    ("PDF files", "*.pdf"),
                    ("All files", "*.*")
                ]
            )

            if file_path:
                # Generate demo report
                report_content = f"""DermatoGemma Multi-Detection System v2.0
Medical Analysis Report

Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Image: {self.current_image}

ANALYSIS RESULTS:
- System: DermatoGemma Multi-Detection v2.0
- Analysis Type: Multi-condition dermatological screening
- Processing Mode: {'Demo' if self.analysis_engine is None else 'Full Analysis'}

FINDINGS:
- Total lesions detected: 2
- Primary concern: Seborrheic Keratosis
- Risk level: Medium
- Confidence: 78%

RECOMMENDATIONS:
- Monitor lesion for changes
- Consider dermatologist consultation
- Follow-up in 3-6 months
- Document findings in medical records

DISCLAIMER:
This analysis is for screening purposes only and should not replace
professional medical diagnosis. Always consult qualified dermatologists
for definitive diagnosis and treatment recommendations.

Generated by DermatoGemma Multi-Detection System v2.0
"""

                with open(file_path, 'w') as f:
                    f.write(report_content)

                messagebox.showinfo("Success", f"Report saved to: {file_path}")
                logger.info(f"Report generated: {file_path}")

        except Exception as e:
            logger.error(f"Report generation failed: {e}")
            messagebox.showerror("Error", f"Failed to generate report: {str(e)}")

    def _create_status_bar(self):
        """Create status bar at the bottom of the window"""
        try:
            # Create status bar frame
            status_frame = tk.Frame(self.root, bg=self.colors['dark'], height=30)
            status_frame.pack(side='bottom', fill='x')
            status_frame.pack_propagate(False)

            # Status label
            self.status_label = tk.Label(
                status_frame,
                text="Ready for dermatological analysis",
                font=self.fonts['small'],
                bg=self.colors['dark'],
                fg=self.colors['light'],
                anchor='w'
            )
            self.status_label.pack(side='left', padx=10, pady=5)

            # Version label
            version_label = tk.Label(
                status_frame,
                text="DermatoGemma v2.0",
                font=self.fonts['small'],
                bg=self.colors['dark'],
                fg=self.colors['light']
            )
            version_label.pack(side='right', padx=10, pady=5)

            logger.info("Status bar created successfully")

        except Exception as e:
            logger.error(f"Failed to create status bar: {e}")

    def update_status(self, message):
        """Update status bar message"""
        try:
            if hasattr(self, 'status_label'):
                self.status_label.config(text=message)
        except Exception as e:
            logger.error(f"Failed to update status: {e}")

    def _update_results_from_analysis(self, results):
        """Update interface with real analysis results"""
        try:
            # This method would be implemented to handle real analysis results
            # For now, we'll use the demo update
            if results.get('success', False):
                overall_assessment = results.get('overall_assessment', {})
                risk_level = overall_assessment.get('overall_risk_level', 'unknown')

                # Update quick results
                summary = f"Analysis completed successfully!\n\n"
                summary += f"Risk Level: {risk_level.upper()}\n"
                summary += f"Lesions detected: {overall_assessment.get('total_lesions_analyzed', 0)}\n"

                self._update_quick_results(summary)
                self.update_status("Analysis completed successfully")
            else:
                self._update_quick_results("Analysis failed. Please try again.")
                self.update_status("Analysis failed")

        except Exception as e:
            logger.error(f"Failed to update results from analysis: {e}")
            self._perform_demo_analysis()  # Fallback to demo
