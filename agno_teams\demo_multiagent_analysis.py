#!/usr/bin/env python3
"""
🚀 Demo: DermatoGemma Multi-Agent System
Demonstração do sistema de multiagentes para análise dermatológica
"""

import asyncio
import json
import time
from pathlib import Path
from typing import Dict, Any
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import our multi-agent system
from dermatology_multiagent_system import DermatologyMultiAgentSystem
from config import get_config

class DermatologyDemo:
    """Demonstration class for the multi-agent dermatology system"""
    
    def __init__(self):
        self.config = get_config()
        self.config.setup_directories()
        
        # Validate configuration
        issues = self.config.validate_config()
        if issues:
            logger.warning(f"Configuration issues: {issues}")
        
        # Initialize the multi-agent system
        self.derma_system = DermatologyMultiAgentSystem(
            use_openai=self.config.USE_OPENAI
        )
        
        logger.info("🤖 DermatoGemma Multi-Agent Demo initialized")
    
    def demo_comprehensive_analysis(self):
        """Demonstrate comprehensive skin lesion analysis"""
        print("\n" + "="*80)
        print("🔬 DEMO: Comprehensive Dermatological Analysis")
        print("="*80)
        
        # Example patient data
        patient_context = {
            "age": 45,
            "gender": "female",
            "skin_type": "fair (Type II)",
            "family_history": "melanoma in mother",
            "personal_history": "multiple atypical moles",
            "sun_exposure": "high recreational exposure",
            "previous_biopsies": "2 benign nevi removed"
        }
        
        print(f"👤 Patient Context:")
        for key, value in patient_context.items():
            print(f"   {key.replace('_', ' ').title()}: {value}")
        
        # Simulate analysis (in real use, you'd provide actual image path)
        example_image = "test_images/melanoma/suspicious_lesion.jpg"
        
        print(f"\n🖼️ Analyzing lesion image: {example_image}")
        print("📋 Analysis workflow:")
        print("   1. ABCDE Specialist - Melanoma risk assessment")
        print("   2. Multi-Condition Specialist - Differential diagnosis")
        print("   3. Medical Validator - Clinical guidelines compliance")
        print("   4. Clinical Synthesizer - Integrated assessment")
        print("   5. Clinical Advisor - Recommendations and referrals")
        
        try:
            start_time = time.time()
            
            # Note: This would work with actual image files
            # For demo purposes, we'll show the expected workflow
            print("\n⏳ Starting multi-agent analysis...")
            
            # Simulate the analysis process
            self._simulate_analysis_workflow()
            
            analysis_time = time.time() - start_time
            print(f"\n✅ Analysis completed in {analysis_time:.2f} seconds")
            
        except Exception as e:
            logger.error(f"❌ Analysis failed: {e}")
            print(f"❌ Demo analysis failed: {e}")
    
    def _simulate_analysis_workflow(self):
        """Simulate the multi-agent analysis workflow"""
        
        # Simulate ABCDE Analysis
        print("\n🔍 ABCDE Specialist analyzing...")
        time.sleep(1)
        abcde_results = {
            "asymmetry_score": 0.8,
            "border_irregularity": 0.7,
            "color_variation": 0.9,
            "diameter_mm": 8.5,
            "evolution_detected": True,
            "risk_level": "high",
            "confidence": 0.85
        }
        print(f"   ✅ ABCDE Analysis: {abcde_results['risk_level']} risk (confidence: {abcde_results['confidence']:.2%})")
        
        # Simulate Multi-Condition Analysis
        print("\n🔬 Multi-Condition Specialist analyzing...")
        time.sleep(1.5)
        multi_condition_results = {
            "melanoma_probability": 0.78,
            "basal_cell_carcinoma": 0.15,
            "squamous_cell_carcinoma": 0.05,
            "benign_nevus": 0.02,
            "primary_diagnosis": "Malignant Melanoma",
            "confidence": 0.78
        }
        print(f"   ✅ Primary Diagnosis: {multi_condition_results['primary_diagnosis']} ({multi_condition_results['confidence']:.2%})")
        
        # Simulate Medical Validation
        print("\n✅ Medical Validator reviewing...")
        time.sleep(0.8)
        validation_results = {
            "guidelines_compliance": True,
            "consistency_check": True,
            "red_flags": ["rapid growth", "color variation", "irregular border"],
            "validation_score": 0.92
        }
        print(f"   ✅ Validation: Guidelines compliant (score: {validation_results['validation_score']:.2%})")
        
        # Simulate Clinical Synthesis
        print("\n🧠 Clinical Synthesizer integrating findings...")
        time.sleep(1.2)
        synthesis_results = {
            "integrated_diagnosis": "Suspicious melanoma - urgent evaluation required",
            "confidence_level": 0.82,
            "key_findings": [
                "ABCDE criteria strongly positive",
                "High melanoma probability on multi-condition analysis",
                "Concerning evolution pattern",
                "Patient risk factors present"
            ]
        }
        print(f"   ✅ Synthesis: {synthesis_results['integrated_diagnosis']}")
        
        # Simulate Clinical Recommendations
        print("\n👨‍⚕️ Clinical Advisor generating recommendations...")
        time.sleep(1.0)
        recommendations = {
            "urgency_level": "urgent",
            "referral_needed": True,
            "referral_timeframe": "within 48 hours",
            "follow_up": "dermatologist evaluation",
            "additional_tests": ["dermoscopy", "possible biopsy"],
            "patient_instructions": [
                "Avoid sun exposure",
                "Monitor for changes",
                "Seek immediate care if bleeding occurs"
            ]
        }
        print(f"   ✅ Recommendations: {recommendations['urgency_level']} referral {recommendations['referral_timeframe']}")
        
        # Display final summary
        self._display_analysis_summary(abcde_results, multi_condition_results, 
                                     validation_results, synthesis_results, recommendations)
    
    def _display_analysis_summary(self, abcde, multi_condition, validation, synthesis, recommendations):
        """Display comprehensive analysis summary"""
        print("\n" + "="*80)
        print("📊 COMPREHENSIVE ANALYSIS SUMMARY")
        print("="*80)
        
        print(f"🎯 Primary Diagnosis: {multi_condition['primary_diagnosis']}")
        print(f"🔢 Confidence Score: {synthesis['confidence_level']:.2%}")
        print(f"⚠️ Risk Level: {abcde['risk_level'].upper()}")
        print(f"🚨 Urgency: {recommendations['urgency_level'].upper()}")
        print(f"👨‍⚕️ Referral: {'YES' if recommendations['referral_needed'] else 'NO'} ({recommendations['referral_timeframe']})")
        
        print(f"\n🔍 Key Findings:")
        for finding in synthesis['key_findings']:
            print(f"   • {finding}")
        
        print(f"\n💊 Recommendations:")
        for instruction in recommendations['patient_instructions']:
            print(f"   • {instruction}")
        
        print(f"\n🧪 Suggested Tests:")
        for test in recommendations['additional_tests']:
            print(f"   • {test}")
    
    def demo_high_risk_analysis(self):
        """Demonstrate high-risk lesion analysis"""
        print("\n" + "="*80)
        print("🚨 DEMO: High-Risk Lesion Analysis")
        print("="*80)
        
        print("🔥 Specialized high-risk assessment team activated")
        print("👥 Team members: ABCDE Specialist, Multi-Condition Specialist, Medical Validator")
        print("🎯 Focus: Malignancy indicators, urgent assessment, immediate referral needs")
        
        # Simulate high-risk analysis
        print("\n⚡ High-priority analysis workflow:")
        print("   • Parallel analysis by all specialists")
        print("   • Focus on malignancy indicators")
        print("   • Immediate risk stratification")
        print("   • Urgent referral determination")
        
        time.sleep(2)
        print("\n✅ High-risk analysis completed")
        print("🚨 Result: URGENT REFERRAL REQUIRED - Suspected melanoma with high-risk features")
    
    def demo_differential_diagnosis(self):
        """Demonstrate differential diagnosis analysis"""
        print("\n" + "="*80)
        print("🔍 DEMO: Differential Diagnosis Analysis")
        print("="*80)
        
        print("🧠 Specialized differential diagnosis team activated")
        print("👥 Team members: Multi-Condition Specialist, Clinical Synthesizer")
        print("🎯 Focus: Multiple diagnoses, rare presentations, diagnostic confidence")
        
        # Simulate differential diagnosis
        differential_diagnoses = [
            {"condition": "Malignant Melanoma", "probability": 0.65, "confidence": "high"},
            {"condition": "Atypical Nevus", "probability": 0.20, "confidence": "medium"},
            {"condition": "Seborrheic Keratosis", "probability": 0.10, "confidence": "low"},
            {"condition": "Basal Cell Carcinoma", "probability": 0.05, "confidence": "low"}
        ]
        
        print("\n📋 Differential Diagnosis Results:")
        for i, dx in enumerate(differential_diagnoses, 1):
            print(f"   {i}. {dx['condition']}: {dx['probability']:.2%} (confidence: {dx['confidence']})")
        
        print("\n🔬 Recommended additional testing:")
        print("   • Dermoscopy for detailed surface analysis")
        print("   • Biopsy for histopathological confirmation")
        print("   • Photography for evolution tracking")
    
    def demo_team_metrics(self):
        """Demonstrate team performance metrics"""
        print("\n" + "="*80)
        print("📈 DEMO: Team Performance Metrics")
        print("="*80)
        
        # Get actual metrics from the system
        metrics = self.derma_system.get_team_metrics()
        
        print("🤖 Multi-Agent System Statistics:")
        print(f"   • Total Team Members: {metrics.get('team_members', 5)}")
        print(f"   • Specialized Teams: {metrics.get('specialized_teams', 2)}")
        print(f"   • Memory Entries: {metrics.get('memory_entries', 0)}")
        print(f"   • Total Analyses: {metrics.get('total_analyses', 0)}")
        
        print("\n⚡ Performance Capabilities:")
        print("   • Parallel processing for urgent cases")
        print("   • Persistent memory across sessions")
        print("   • Knowledge base integration")
        print("   • Structured medical output")
        print("   • Clinical guidelines compliance")
    
    def run_full_demo(self):
        """Run the complete demonstration"""
        print("🏥 DermatoGemma Multi-Agent System v2.0 - DEMONSTRATION")
        print("🤖 Powered by Agno Teams")
        print("=" * 80)
        
        try:
            # Run all demo scenarios
            self.demo_comprehensive_analysis()
            self.demo_high_risk_analysis()
            self.demo_differential_diagnosis()
            self.demo_team_metrics()
            
            print("\n" + "="*80)
            print("✅ DEMONSTRATION COMPLETED SUCCESSFULLY")
            print("🎉 DermatoGemma Multi-Agent System is ready for clinical use!")
            print("="*80)
            
        except Exception as e:
            logger.error(f"❌ Demo failed: {e}")
            print(f"\n❌ Demo failed: {e}")

def main():
    """Main demonstration function"""
    demo = DermatologyDemo()
    demo.run_full_demo()

if __name__ == "__main__":
    main()
