#!/usr/bin/env python3
"""
DermatoGemma Multi-Detection System v2.0
Warning Suppression and Environment Configuration

This script fixes common warnings and optimizes the environment for medical AI applications.
Addresses:
- Cryptography deprecation warnings (paramiko)
- TensorFlow oneDNN optimization messages
- Other common ML/AI library warnings
"""

import os
import sys
import warnings
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WarningSuppressionManager:
    """
    Professional warning suppression and environment optimization
    for medical AI applications
    """
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.applied_fixes = []
        
    def suppress_cryptography_warnings(self):
        """Suppress cryptography deprecation warnings from paramiko"""
        try:
            # Filter cryptography deprecation warnings more aggressively
            warnings.filterwarnings("ignore", category=DeprecationWarning, module="paramiko")
            warnings.filterwarnings("ignore", message=".*TripleDES.*")
            warnings.filterwarnings("ignore", message=".*Blowfish.*")
            warnings.filterwarnings("ignore", message=".*cryptography.*")
            warnings.filterwarnings("ignore", message=".*decrepit.*")

            # Global cryptography warning suppression
            warnings.filterwarnings("ignore", category=DeprecationWarning, message=".*cryptography.*")

            # Set environment variables to suppress cryptography warnings
            os.environ['CRYPTOGRAPHY_DONT_BUILD_RUST'] = '1'
            os.environ['PYTHONWARNINGS'] = 'ignore::DeprecationWarning'

            self.applied_fixes.append("✅ Cryptography warnings suppressed")
            logger.info("✅ Cryptography deprecation warnings suppressed")

        except Exception as e:
            logger.warning(f"⚠️ Failed to suppress cryptography warnings: {e}")
    
    def optimize_tensorflow_environment(self):
        """Optimize TensorFlow environment and suppress verbose messages"""
        try:
            # Suppress TensorFlow info messages
            os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # 0=all, 1=info, 2=warning, 3=error
            
            # Disable oneDNN optimizations if causing issues (optional)
            # os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
            
            # Keep oneDNN enabled but suppress the info messages
            os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'
            
            # Suppress TensorFlow warnings
            warnings.filterwarnings("ignore", category=FutureWarning, module="tensorflow")
            warnings.filterwarnings("ignore", category=UserWarning, module="tensorflow")
            
            self.applied_fixes.append("✅ TensorFlow environment optimized")
            logger.info("✅ TensorFlow environment optimized and messages suppressed")
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to optimize TensorFlow environment: {e}")
    
    def suppress_ml_library_warnings(self):
        """Suppress common ML library warnings"""
        try:
            # Suppress scikit-learn warnings
            warnings.filterwarnings("ignore", category=FutureWarning, module="sklearn")
            warnings.filterwarnings("ignore", category=UserWarning, module="sklearn")
            
            # Suppress numpy warnings
            warnings.filterwarnings("ignore", category=FutureWarning, module="numpy")
            warnings.filterwarnings("ignore", category=RuntimeWarning, module="numpy")
            
            # Suppress matplotlib warnings
            warnings.filterwarnings("ignore", category=UserWarning, module="matplotlib")
            
            # Suppress PIL/Pillow warnings
            warnings.filterwarnings("ignore", category=UserWarning, module="PIL")
            
            # Suppress OpenCV warnings
            warnings.filterwarnings("ignore", category=UserWarning, module="cv2")
            
            self.applied_fixes.append("✅ ML library warnings suppressed")
            logger.info("✅ ML library warnings suppressed")
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to suppress ML library warnings: {e}")
    
    def optimize_gpu_environment(self):
        """Optimize GPU environment if available"""
        try:
            # CUDA optimizations
            os.environ['CUDA_CACHE_DISABLE'] = '0'
            os.environ['CUDA_CACHE_MAXSIZE'] = '**********'  # 2GB cache
            
            # Memory growth for TensorFlow GPU
            os.environ['TF_FORCE_GPU_ALLOW_GROWTH'] = 'true'
            
            # Suppress CUDA warnings
            os.environ['TF_CUDA_PATHS'] = ''
            
            self.applied_fixes.append("✅ GPU environment optimized")
            logger.info("✅ GPU environment optimized")
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to optimize GPU environment: {e}")
    
    def set_medical_ai_optimizations(self):
        """Set optimizations specific to medical AI applications"""
        try:
            # Optimize for medical image processing
            os.environ['OMP_NUM_THREADS'] = str(min(8, os.cpu_count()))  # Limit threads
            os.environ['MKL_NUM_THREADS'] = str(min(8, os.cpu_count()))
            
            # Memory optimizations
            os.environ['MALLOC_TRIM_THRESHOLD_'] = '100000'
            
            # Python optimizations
            os.environ['PYTHONHASHSEED'] = '0'  # Reproducible results
            
            self.applied_fixes.append("✅ Medical AI optimizations applied")
            logger.info("✅ Medical AI optimizations applied")
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to apply medical AI optimizations: {e}")
    
    def create_environment_script(self):
        """Create a script to set environment variables"""
        try:
            env_script_content = '''@echo off
REM DermatoGemma Multi-Detection System v2.0
REM Environment Configuration Script

echo 🏥 Setting up DermatoGemma environment...

REM Suppress TensorFlow messages
set TF_CPP_MIN_LOG_LEVEL=2
set TF_ENABLE_ONEDNN_OPTS=1

REM Cryptography optimizations
set CRYPTOGRAPHY_DONT_BUILD_RUST=1

REM GPU optimizations
set TF_FORCE_GPU_ALLOW_GROWTH=true
set CUDA_CACHE_DISABLE=0
set CUDA_CACHE_MAXSIZE=**********

REM Threading optimizations
set OMP_NUM_THREADS=8
set MKL_NUM_THREADS=8

REM Python optimizations
set PYTHONHASHSEED=0

echo ✅ Environment configured for optimal performance
echo 🚀 Ready to launch DermatoGemma!
'''
            
            env_script_path = self.project_root / "setup_environment.bat"
            with open(env_script_path, 'w') as f:
                f.write(env_script_content)
            
            self.applied_fixes.append(f"✅ Environment script created: {env_script_path}")
            logger.info(f"✅ Environment script created: {env_script_path}")
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to create environment script: {e}")
    
    def apply_all_fixes(self):
        """Apply all warning suppressions and optimizations"""
        logger.info("🔧 Applying warning suppressions and optimizations...")
        logger.info("=" * 60)
        
        # Apply all fixes
        self.suppress_cryptography_warnings()
        self.optimize_tensorflow_environment()
        self.suppress_ml_library_warnings()
        self.optimize_gpu_environment()
        self.set_medical_ai_optimizations()
        self.create_environment_script()
        
        # Summary
        logger.info("=" * 60)
        logger.info("🎯 Applied Fixes Summary:")
        for fix in self.applied_fixes:
            logger.info(f"  {fix}")
        
        logger.info("=" * 60)
        logger.info("✅ All optimizations applied successfully!")
        logger.info("🏥 DermatoGemma environment is now optimized for medical AI")
        
        return len(self.applied_fixes)

def apply_runtime_fixes():
    """Apply fixes at runtime (call this in main applications)"""
    try:
        # Quick runtime fixes without logging
        import warnings
        import os

        # Suppress cryptography warnings more aggressively
        warnings.filterwarnings("ignore", category=DeprecationWarning, module="paramiko")
        warnings.filterwarnings("ignore", message=".*TripleDES.*")
        warnings.filterwarnings("ignore", message=".*Blowfish.*")
        warnings.filterwarnings("ignore", message=".*cryptography.*")
        warnings.filterwarnings("ignore", message=".*decrepit.*")
        warnings.filterwarnings("ignore", category=DeprecationWarning, message=".*cryptography.*")

        # Suppress TensorFlow warnings
        warnings.filterwarnings("ignore", category=FutureWarning, module="tensorflow")
        warnings.filterwarnings("ignore", category=UserWarning, module="tensorflow")

        # Set environment variables
        os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
        os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'
        os.environ['CRYPTOGRAPHY_DONT_BUILD_RUST'] = '1'
        os.environ['PYTHONWARNINGS'] = 'ignore::DeprecationWarning'

        return True

    except Exception:
        return False

def main():
    """Main function to apply all fixes"""
    try:
        print("🏥 DermatoGemma Multi-Detection System v2.0")
        print("🔧 Warning Suppression and Environment Optimization")
        print("=" * 60)
        
        # Create and apply fixes
        manager = WarningSuppressionManager()
        fixes_applied = manager.apply_all_fixes()
        
        print("=" * 60)
        print(f"🎉 Successfully applied {fixes_applied} optimizations!")
        print("💡 To use optimizations:")
        print("   1. Run: python fix_warnings.py")
        print("   2. Or: setup_environment.bat")
        print("   3. Then launch: python main.py")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to apply fixes: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
