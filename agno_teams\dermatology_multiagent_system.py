#!/usr/bin/env python3
"""
🏥 DermatoGemma Multi-Agent System v2.0 - Agno Teams Implementation
Sistema de Multiagentes Autônomos para Análise Dermatológica Avançada

Este sistema utiliza Agno Teams para criar uma equipe de agentes especializados
que trabalham em conjunto para fornecer análises dermatológicas precisas e abrangentes.
"""

import os
import sys
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
import numpy as np
from PIL import Image
import base64
import io

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Agno Teams imports
from agno.agent import Agent
from agno.team import Team
from agno.models.openai import OpenAIChat
from agno.models.ollama import Ollama
from agno.memory.v2.db.sqlite import SqliteMemoryDb
from agno.memory.v2.memory import Memory
from agno.knowledge.url import UrlKnowledge
from agno.vectordb.lancedb import LanceDb, SearchType
from agno.embedder.openai import OpenAIEmbedder
from pydantic import BaseModel

# Import existing DermatoGemma components
from core.gemma_derma_handler import GemmaDermatologyHandlerV2
from core.abcde_analyzer import AdvancedABCDEAnalyzer
from core.multi_condition_engine import MultiConditionAnalysisEngine
from core.medical_validation_system import MedicalValidationSystem
from data.medical_reference_database import medical_db

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DermatologyAnalysisResult(BaseModel):
    """Structured output for dermatology analysis"""
    primary_diagnosis: str
    confidence_score: float
    risk_level: str
    abcde_analysis: Dict[str, Any]
    multi_condition_results: Dict[str, Any]
    clinical_recommendations: List[str]
    urgency_level: str
    referral_needed: bool
    follow_up_timeframe: str
    key_findings: List[str]
    differential_diagnoses: List[str]

class DermatologyTools:
    """Custom tools for dermatology analysis"""
    
    def __init__(self):
        self.gemma_handler = GemmaDermatologyHandlerV2(lazy_load=True)
        self.abcde_analyzer = AdvancedABCDEAnalyzer()
        self.multi_condition_engine = MultiConditionAnalysisEngine()
        self.validation_system = MedicalValidationSystem()
    
    def analyze_lesion_abcde(self, image_data: str, description: str = "") -> Dict[str, Any]:
        """Perform ABCDE analysis on lesion image"""
        try:
            # Convert base64 to numpy array
            image_bytes = base64.b64decode(image_data)
            image = Image.open(io.BytesIO(image_bytes))
            image_array = np.array(image)
            
            # Perform ABCDE analysis
            results = self.abcde_analyzer.analyze_lesion_abcde(image_array)
            
            logger.info(f"✅ ABCDE analysis completed - Risk: {results.get('risk_assessment', {}).get('level', 'unknown')}")
            return results
            
        except Exception as e:
            logger.error(f"❌ ABCDE analysis failed: {e}")
            return {"error": str(e), "success": False}
    
    def analyze_multi_condition(self, image_data: str, abcde_results: Dict = None) -> Dict[str, Any]:
        """Perform multi-condition analysis"""
        try:
            # Convert base64 to numpy array
            image_bytes = base64.b64decode(image_data)
            image = Image.open(io.BytesIO(image_bytes))
            image_array = np.array(image)
            
            # Perform multi-condition analysis
            results = self.gemma_handler.analyze_multi_condition(
                image_array, 
                abcde_results=abcde_results
            )
            
            logger.info(f"✅ Multi-condition analysis completed")
            return results
            
        except Exception as e:
            logger.error(f"❌ Multi-condition analysis failed: {e}")
            return {"error": str(e), "success": False}
    
    def validate_medical_findings(self, analysis_results: Dict) -> Dict[str, Any]:
        """Validate medical findings against clinical guidelines"""
        try:
            validation_results = self.validation_system.validate_analysis_results(analysis_results)
            logger.info(f"✅ Medical validation completed")
            return validation_results
            
        except Exception as e:
            logger.error(f"❌ Medical validation failed: {e}")
            return {"error": str(e), "success": False}

class DermatologyMultiAgentSystem:
    """
    🤖 Sistema de Multiagentes Autônomos para Dermatologia
    
    Utiliza Agno Teams para coordenar agentes especializados:
    - Agente de Análise ABCDE
    - Agente de Análise Multi-Condição
    - Agente de Validação Médica
    - Agente de Síntese Clínica
    - Agente de Recomendações
    """
    
    def __init__(self, use_openai: bool = False):
        self.use_openai = use_openai
        self.tools = DermatologyTools()
        
        # Setup memory and knowledge base
        self._setup_memory_and_knowledge()
        
        # Create specialized agents
        self._create_specialized_agents()
        
        # Create the main dermatology team
        self._create_dermatology_team()
        
        logger.info("🤖 DermatoGemma Multi-Agent System initialized")
    
    def _setup_memory_and_knowledge(self):
        """Setup memory and knowledge base for the team"""
        # Create memory database
        memory_db = SqliteMemoryDb(
            table_name="dermatology_memory", 
            db_file="agno_teams/dermatology_memory.db"
        )
        self.memory = Memory(db=memory_db)
        
        # Setup knowledge base with medical literature
        cwd = Path(__file__).parent
        tmp_dir = cwd.joinpath("tmp")
        tmp_dir.mkdir(parents=True, exist_ok=True)
        
        # Medical knowledge base (you can add URLs to medical literature)
        self.medical_knowledge = UrlKnowledge(
            urls=[
                "https://www.aad.org/public/diseases/skin-cancer/types/common/melanoma",
                "https://www.who.int/news-room/fact-sheets/detail/ultraviolet-radiation"
            ],
            vector_db=LanceDb(
                uri=str(tmp_dir.joinpath("medical_lancedb")),
                table_name="medical_docs",
                search_type=SearchType.hybrid,
                embedder=OpenAIEmbedder(id="text-embedding-3-small") if self.use_openai else None,
            ),
        )
    
    def _get_model(self):
        """Get the appropriate model based on configuration"""
        if self.use_openai:
            return OpenAIChat(id="gpt-4o")
        else:
            return Ollama(id="gemma3n:e4b", host="http://localhost:11434")
    
    def _create_specialized_agents(self):
        """Create specialized dermatology agents"""
        
        # 1. ABCDE Analysis Agent
        self.abcde_agent = Agent(
            name="ABCDE Specialist",
            role="Expert in ABCDE melanoma analysis",
            model=self._get_model(),
            description=(
                "Specialized agent for performing comprehensive ABCDE analysis of skin lesions. "
                "Expert in asymmetry, border irregularity, color variation, diameter assessment, "
                "and evolution tracking for melanoma detection."
            ),
            instructions=[
                "Perform detailed ABCDE analysis on skin lesion images",
                "Assess asymmetry using advanced geometric analysis",
                "Evaluate border irregularity with precision measurements",
                "Analyze color variation using multi-spectral techniques",
                "Measure diameter and track evolution over time",
                "Provide risk assessment based on ABCDE criteria",
                "Always include confidence scores and clinical significance"
            ],
            tools=[self.tools.analyze_lesion_abcde],
            add_datetime_to_instructions=True,
        )
        
        # 2. Multi-Condition Analysis Agent
        self.multi_condition_agent = Agent(
            name="Multi-Condition Specialist",
            role="Expert in multi-condition dermatological analysis",
            model=self._get_model(),
            description=(
                "Specialized agent for analyzing multiple dermatological conditions simultaneously. "
                "Expert in identifying various skin conditions including melanoma, basal cell carcinoma, "
                "squamous cell carcinoma, and benign lesions."
            ),
            instructions=[
                "Analyze skin lesions for multiple dermatological conditions",
                "Provide probability assessments for each condition",
                "Consider differential diagnoses",
                "Integrate visual features with clinical knowledge",
                "Provide detailed condition-specific analysis",
                "Always include confidence intervals and uncertainty quantification"
            ],
            tools=[self.tools.analyze_multi_condition],
            add_datetime_to_instructions=True,
        )
        
        # 3. Medical Validation Agent
        self.validation_agent = Agent(
            name="Medical Validator",
            role="Expert in medical validation and clinical guidelines",
            model=self._get_model(),
            description=(
                "Specialized agent for validating medical findings against clinical guidelines. "
                "Expert in ensuring compliance with dermatological standards and medical protocols."
            ),
            instructions=[
                "Validate all medical findings against clinical guidelines",
                "Check for consistency across different analyses",
                "Ensure compliance with medical standards",
                "Identify potential red flags or concerning findings",
                "Provide quality assurance for medical recommendations",
                "Flag any inconsistencies or unusual findings"
            ],
            tools=[self.tools.validate_medical_findings],
            add_datetime_to_instructions=True,
        )
        
        # 4. Clinical Synthesis Agent
        self.synthesis_agent = Agent(
            name="Clinical Synthesizer",
            role="Expert in synthesizing clinical findings",
            model=self._get_model(),
            description=(
                "Specialized agent for synthesizing multiple analysis results into coherent clinical assessments. "
                "Expert in integrating ABCDE, multi-condition, and validation results."
            ),
            instructions=[
                "Synthesize results from ABCDE, multi-condition, and validation analyses",
                "Create coherent clinical assessments",
                "Identify the most likely primary diagnosis",
                "Provide comprehensive differential diagnoses",
                "Ensure all findings are clinically consistent",
                "Generate clear, actionable clinical summaries"
            ],
            add_datetime_to_instructions=True,
        )
        
        # 5. Recommendations Agent
        self.recommendations_agent = Agent(
            name="Clinical Advisor",
            role="Expert in clinical recommendations and patient care",
            model=self._get_model(),
            description=(
                "Specialized agent for providing clinical recommendations, referral guidance, "
                "and patient care instructions based on dermatological findings."
            ),
            instructions=[
                "Provide specific clinical recommendations based on findings",
                "Determine urgency levels and referral needs",
                "Suggest appropriate follow-up timeframes",
                "Recommend additional tests or procedures if needed",
                "Provide patient education and care instructions",
                "Ensure recommendations follow clinical best practices"
            ],
            add_datetime_to_instructions=True,
        )

    def _create_dermatology_team(self):
        """Create the main dermatology analysis team"""

        self.dermatology_team = Team(
            name="DermatoGemma Expert Team",
            mode="coordinate",
            model=self._get_model(),
            members=[
                self.abcde_agent,
                self.multi_condition_agent,
                self.validation_agent,
                self.synthesis_agent,
                self.recommendations_agent
            ],
            description=(
                "Elite team of dermatology specialists providing comprehensive skin lesion analysis. "
                "Combines ABCDE analysis, multi-condition assessment, medical validation, "
                "clinical synthesis, and expert recommendations."
            ),
            instructions=[
                "Coordinate a comprehensive dermatological analysis workflow:",
                "1. First, ask the ABCDE Specialist to perform detailed ABCDE analysis",
                "2. Then, ask the Multi-Condition Specialist to analyze for multiple conditions",
                "3. Have the Medical Validator review all findings for clinical accuracy",
                "4. Ask the Clinical Synthesizer to integrate all results into a coherent assessment",
                "5. Finally, have the Clinical Advisor provide specific recommendations",
                "Ensure each step builds upon previous findings",
                "Maintain clinical accuracy and patient safety throughout",
                "Provide comprehensive, actionable medical insights"
            ],
            memory=self.memory,
            knowledge=self.medical_knowledge,
            response_model=DermatologyAnalysisResult,
            enable_agentic_memory=True,
            enable_session_summaries=True,
            add_datetime_to_instructions=True,
            show_members_responses=True,
            markdown=True,
            debug_mode=False,
        )

        # Create specialized sub-teams for complex cases
        self._create_specialized_teams()

    def _create_specialized_teams(self):
        """Create specialized sub-teams for specific scenarios"""

        # High-Risk Assessment Team
        self.high_risk_team = Team(
            name="High-Risk Assessment Team",
            mode="collaborate",
            model=self._get_model(),
            members=[self.abcde_agent, self.multi_condition_agent, self.validation_agent],
            description="Specialized team for high-risk lesion assessment",
            instructions=[
                "Focus on high-risk features and malignancy indicators",
                "Provide urgent assessment for concerning lesions",
                "Prioritize patient safety and early detection",
                "Recommend immediate referral when indicated"
            ],
            memory=self.memory,
            show_members_responses=True,
        )

        # Differential Diagnosis Team
        self.differential_team = Team(
            name="Differential Diagnosis Team",
            mode="collaborate",
            model=self._get_model(),
            members=[self.multi_condition_agent, self.synthesis_agent],
            description="Specialized team for complex differential diagnoses",
            instructions=[
                "Provide comprehensive differential diagnosis",
                "Consider rare and atypical presentations",
                "Analyze subtle diagnostic features",
                "Recommend additional testing when needed"
            ],
            memory=self.memory,
            show_members_responses=True,
        )

    def analyze_skin_lesion(self, image_path: str, patient_context: Dict = None) -> DermatologyAnalysisResult:
        """
        Perform comprehensive skin lesion analysis using the multi-agent team

        Args:
            image_path: Path to the skin lesion image
            patient_context: Optional patient context information

        Returns:
            Comprehensive dermatology analysis result
        """
        try:
            logger.info("🔬 Starting comprehensive dermatological analysis...")

            # Load and encode image
            image_data = self._encode_image(image_path)

            # Prepare analysis context
            context = {
                "image_data": image_data,
                "patient_context": patient_context or {},
                "analysis_timestamp": str(Path(__file__).stat().st_mtime)
            }

            # Run the main dermatology team analysis
            analysis_prompt = f"""
            Perform a comprehensive dermatological analysis of the provided skin lesion image.

            Image: {image_path}
            Patient Context: {patient_context}

            Please coordinate the team to provide:
            1. Detailed ABCDE analysis
            2. Multi-condition assessment
            3. Medical validation
            4. Clinical synthesis
            5. Expert recommendations

            Ensure the analysis is thorough, accurate, and clinically actionable.
            """

            result = self.dermatology_team.run(analysis_prompt)

            logger.info("✅ Comprehensive dermatological analysis completed")
            return result.content

        except Exception as e:
            logger.error(f"❌ Analysis failed: {e}")
            raise

    def analyze_high_risk_lesion(self, image_path: str, patient_context: Dict = None) -> Dict[str, Any]:
        """
        Specialized analysis for high-risk lesions
        """
        try:
            logger.info("🚨 Starting high-risk lesion analysis...")

            image_data = self._encode_image(image_path)

            analysis_prompt = f"""
            URGENT: Analyze this potentially high-risk skin lesion.

            Focus on:
            - Malignancy indicators
            - ABCDE red flags
            - Immediate risk assessment
            - Urgent referral needs

            Image: {image_path}
            Patient Context: {patient_context}
            """

            result = self.high_risk_team.run(analysis_prompt)

            logger.info("✅ High-risk analysis completed")
            return result.content

        except Exception as e:
            logger.error(f"❌ High-risk analysis failed: {e}")
            raise

    def get_differential_diagnosis(self, image_path: str, primary_findings: Dict = None) -> Dict[str, Any]:
        """
        Get comprehensive differential diagnosis
        """
        try:
            logger.info("🔍 Starting differential diagnosis analysis...")

            image_data = self._encode_image(image_path)

            analysis_prompt = f"""
            Provide comprehensive differential diagnosis for this skin lesion.

            Primary Findings: {primary_findings}

            Consider:
            - Multiple possible diagnoses
            - Rare and atypical presentations
            - Additional testing needs
            - Diagnostic confidence levels

            Image: {image_path}
            """

            result = self.differential_team.run(analysis_prompt)

            logger.info("✅ Differential diagnosis completed")
            return result.content

        except Exception as e:
            logger.error(f"❌ Differential diagnosis failed: {e}")
            raise

    def _encode_image(self, image_path: str) -> str:
        """Encode image to base64 for agent processing"""
        try:
            with open(image_path, "rb") as image_file:
                image_data = base64.b64encode(image_file.read()).decode('utf-8')
            return image_data
        except Exception as e:
            logger.error(f"❌ Image encoding failed: {e}")
            raise

    def get_team_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for the multi-agent team"""
        try:
            metrics = {
                "total_analyses": len(self.memory.get_all()),
                "team_members": len(self.dermatology_team.members),
                "specialized_teams": 2,
                "memory_entries": len(self.memory.get_all()),
            }

            return metrics

        except Exception as e:
            logger.error(f"❌ Metrics retrieval failed: {e}")
            return {}

    def reset_team_memory(self):
        """Reset team memory for fresh analysis"""
        try:
            self.memory.clear()
            logger.info("✅ Team memory reset successfully")
        except Exception as e:
            logger.error(f"❌ Memory reset failed: {e}")

def main():
    """Demonstration of the DermatoGemma Multi-Agent System"""

    print("🏥 DermatoGemma Multi-Agent System v2.0")
    print("=" * 60)

    # Initialize the multi-agent system
    # Set use_openai=True if you have OpenAI API key, False to use local Ollama
    derma_system = DermatologyMultiAgentSystem(use_openai=False)

    # Example analysis (you would replace with actual image path)
    example_image = "test_images/melanoma/example_lesion.jpg"

    if Path(example_image).exists():
        print(f"🔬 Analyzing lesion: {example_image}")

        # Comprehensive analysis
        result = derma_system.analyze_skin_lesion(
            image_path=example_image,
            patient_context={
                "age": 45,
                "gender": "female",
                "skin_type": "fair",
                "family_history": "melanoma in mother"
            }
        )

        print("\n📊 Analysis Results:")
        print(f"Primary Diagnosis: {result.primary_diagnosis}")
        print(f"Confidence: {result.confidence_score:.2%}")
        print(f"Risk Level: {result.risk_level}")
        print(f"Referral Needed: {result.referral_needed}")

        # Get team metrics
        metrics = derma_system.get_team_metrics()
        print(f"\n📈 Team Metrics: {metrics}")

    else:
        print("⚠️ Example image not found. Please provide a valid image path.")
        print("The system is ready for analysis when you provide an image.")

if __name__ == "__main__":
    main()
