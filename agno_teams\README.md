# 🤖 DermatoGemma Multi-Agent System v2.0

## Sistema de Multiagentes Autônomos para Análise Dermatológica

Powered by **Agno Teams** - Sistema avançado de multiagentes para análise dermatológica precisa e abrangente.

---

## 🌟 Características Principais

### 🧠 **Agentes Especializados**
- **ABCDE Specialist**: Análise especializada em critérios ABCDE para melanoma
- **Multi-Condition Specialist**: Análise de múltiplas condições dermatológicas
- **Medical Validator**: Validação contra diretrizes clínicas
- **Clinical Synthesizer**: Síntese inteligente de resultados
- **Clinical Advisor**: Recomendações clínicas e referências

### 🏥 **Equipes Especializadas**
- **Main Team** (Coordinate Mode): Análise abrangente coordenada
- **High-Risk Team** (Collaborate Mode): Avaliação urgente de lesões suspeitas
- **Differential Team** (Collaborate Mode): Diagnóstico diferencial complexo

### 🔬 **Capacidades Avançadas**
- ✅ Análise multimodal (imagem + texto)
- ✅ Memória persistente entre sessões
- ✅ Base de conhecimento médico integrada
- ✅ Validação contra diretrizes clínicas
- ✅ Saída estruturada para integração
- ✅ Processamento paralelo para casos urgentes

---

## 🚀 Instalação Rápida

### 1. **Setup Automático**
```bash
cd agno_teams
python setup_multiagent_system.py
```

### 2. **Instalação Manual**
```bash
# Instalar dependências
pip install -r requirements.txt

# Configurar diretórios
mkdir -p tmp logs data

# Configurar ambiente
cp .env.example .env
```

### 3. **Configuração do Ollama** (Recomendado)
```bash
# Instalar Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Iniciar serviço
ollama serve

# Baixar modelo Gemma
ollama pull gemma3n:e4b
```

---

## 🔧 Configuração

### **Arquivo .env**
```env
# Configuração do Modelo
USE_OPENAI=false                    # true para OpenAI, false para Ollama
OPENAI_API_KEY=your_key_here       # Necessário se USE_OPENAI=true
OLLAMA_HOST=http://localhost:11434  # Host do Ollama
OLLAMA_MODEL=gemma3n:e4b           # Modelo local

# Ambiente
DERMATOGAMMA_ENV=development        # development/production/testing

# Timeouts (segundos)
CONNECTION_TIMEOUT=3600
ANALYSIS_TIMEOUT=3600
```

---

## 💻 Uso Básico

### **Inicialização do Sistema**
```python
from dermatology_multiagent_system import DermatologyMultiAgentSystem

# Inicializar com Ollama (local)
system = DermatologyMultiAgentSystem(use_openai=False)

# Ou com OpenAI (requer API key)
system = DermatologyMultiAgentSystem(use_openai=True)
```

### **Análise Abrangente**
```python
# Contexto do paciente
patient_context = {
    "age": 45,
    "gender": "female", 
    "skin_type": "fair",
    "family_history": "melanoma in mother"
}

# Análise completa
result = system.analyze_skin_lesion(
    image_path="path/to/lesion.jpg",
    patient_context=patient_context
)

print(f"Diagnóstico: {result.primary_diagnosis}")
print(f"Confiança: {result.confidence_score:.2%}")
print(f"Nível de Risco: {result.risk_level}")
print(f"Referência Necessária: {result.referral_needed}")
```

### **Análise de Alto Risco**
```python
# Para lesões suspeitas que requerem avaliação urgente
urgent_result = system.analyze_high_risk_lesion(
    image_path="path/to/suspicious_lesion.jpg",
    patient_context=patient_context
)
```

### **Diagnóstico Diferencial**
```python
# Para casos complexos com múltiplas possibilidades
differential = system.get_differential_diagnosis(
    image_path="path/to/complex_lesion.jpg",
    primary_findings={"melanoma_probability": 0.7}
)
```

---

## 🧪 Demonstração

### **Executar Demo Completa**
```bash
python demo_multiagent_analysis.py
```

### **Testar Instalação**
```bash
python test_installation.py
```

---

## 📊 Estrutura de Saída

### **DermatologyAnalysisResult**
```python
{
    "primary_diagnosis": "Malignant Melanoma",
    "confidence_score": 0.85,
    "risk_level": "high",
    "abcde_analysis": {
        "asymmetry_score": 0.8,
        "border_irregularity": 0.7,
        "color_variation": 0.9,
        "diameter_mm": 8.5,
        "evolution_detected": True
    },
    "multi_condition_results": {
        "melanoma_probability": 0.78,
        "basal_cell_carcinoma": 0.15,
        "squamous_cell_carcinoma": 0.05
    },
    "clinical_recommendations": [
        "Urgent dermatologist referral",
        "Avoid sun exposure",
        "Monitor for changes"
    ],
    "urgency_level": "urgent",
    "referral_needed": True,
    "follow_up_timeframe": "within 48 hours"
}
```

---

## 🏗️ Arquitetura do Sistema

### **Fluxo de Análise Coordenada**
```
📸 Imagem da Lesão
    ↓
🔍 ABCDE Specialist
    ↓
🔬 Multi-Condition Specialist  
    ↓
✅ Medical Validator
    ↓
🧠 Clinical Synthesizer
    ↓
👨‍⚕️ Clinical Advisor
    ↓
📋 Resultado Estruturado
```

### **Equipes Especializadas**
- **Main Team**: Análise coordenada sequencial
- **High-Risk Team**: Análise colaborativa paralela
- **Differential Team**: Diagnóstico diferencial especializado

---

## 🔍 Recursos Avançados

### **Memória Persistente**
```python
# O sistema mantém memória entre sessões
system.memory.get_all()  # Recuperar histórico
system.reset_team_memory()  # Limpar memória
```

### **Base de Conhecimento Médico**
```python
# Integração automática com literatura médica
# URLs configuráveis em config.py
MEDICAL_KNOWLEDGE_URLS = [
    "https://www.aad.org/public/diseases/skin-cancer/...",
    "https://www.who.int/news-room/fact-sheets/..."
]
```

### **Métricas de Performance**
```python
metrics = system.get_team_metrics()
print(f"Análises Totais: {metrics['total_analyses']}")
print(f"Membros da Equipe: {metrics['team_members']}")
```

---

## 🛠️ Desenvolvimento

### **Estrutura do Projeto**
```
agno_teams/
├── dermatology_multiagent_system.py  # Sistema principal
├── config.py                         # Configurações
├── demo_multiagent_analysis.py       # Demonstração
├── setup_multiagent_system.py        # Script de setup
├── requirements.txt                   # Dependências
├── .env                              # Variáveis de ambiente
├── test_installation.py             # Teste de instalação
├── tmp/                              # Arquivos temporários
├── logs/                             # Logs do sistema
└── data/                             # Dados persistentes
```

### **Personalização**
- Modifique `config.py` para ajustar configurações
- Adicione novos agentes especializados
- Configure URLs de conhecimento médico
- Ajuste timeouts para seu ambiente

---

## 🔒 Considerações Médicas

### **⚠️ Importante**
- Este sistema é para **auxílio diagnóstico** apenas
- **NÃO substitui** avaliação médica profissional
- Sempre consulte um dermatologista para diagnóstico definitivo
- Use apenas como ferramenta de triagem e educação

### **🏥 Conformidade Clínica**
- Validação contra diretrizes da AAD (American Academy of Dermatology)
- Critérios ABCDE padrão para melanoma
- Recomendações baseadas em evidências
- Rastreamento de confiança e incerteza

---

## 📞 Suporte

### **Documentação**
- [Agno Teams Documentation](https://docs.agno.com/teams/introduction)
- [DermatoGemma GitHub](https://github.com/your-repo/dermatogamma)

### **Problemas Comuns**
1. **Ollama não conecta**: Verifique se `ollama serve` está rodando
2. **Modelo não encontrado**: Execute `ollama pull gemma3n:e4b`
3. **Erro de memória**: Ajuste timeouts em `.env`
4. **Importação falha**: Execute `pip install -r requirements.txt`

---

## 🎉 Conclusão

O **DermatoGemma Multi-Agent System v2.0** representa um avanço significativo na análise dermatológica assistida por IA, combinando:

- 🤖 **Múltiplos agentes especializados**
- 🧠 **Inteligência colaborativa**
- 🏥 **Conformidade médica**
- ⚡ **Performance otimizada**
- 🔒 **Segurança clínica**

**Pronto para revolucionar a dermatologia com IA autônoma!** 🚀
