#!/usr/bin/env python3
"""
Test ABCDE methods fix for DermatoGemma Multi-Detection System v2.0 - Ollama Edition
"""

# Import warning suppression module FIRST
import suppress_warnings

import sys
from pathlib import Path
import numpy as np

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_missing_methods():
    """Test that all missing ABCDE methods are now implemented"""
    print("🧪 Testing missing ABCDE methods...")
    
    try:
        from core.abcde_analyzer import AdvancedABCDEAnalyzer
        
        # Create analyzer instance
        analyzer = AdvancedABCDEAnalyzer()
        
        # Check if all required methods exist
        required_methods = [
            '_calculate_color_clustering_variation',
            '_interpret_diameter_score',
            '_calculate_analysis_confidence'
        ]
        
        print("   - Checking method existence:")
        for method_name in required_methods:
            if hasattr(analyzer, method_name):
                print(f"     ✅ {method_name}: EXISTS")
            else:
                print(f"     ❌ {method_name}: MISSING")
                return False
        
        print("✅ Missing ABCDE methods test completed successfully!")
        print("   - All required methods are now implemented")
        print("   - No more AttributeError should occur")
        
        return True
        
    except Exception as e:
        print(f"❌ Missing ABCDE methods test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_color_clustering_method():
    """Test the color clustering variation method"""
    print("🧪 Testing color clustering variation method...")
    
    try:
        from core.abcde_analyzer import AdvancedABCDEAnalyzer
        
        # Create analyzer instance
        analyzer = AdvancedABCDEAnalyzer()
        
        # Create test image with multiple colors
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        test_image[:50, :50] = [255, 0, 0]    # Red
        test_image[:50, 50:] = [0, 255, 0]    # Green
        test_image[50:, :50] = [0, 0, 255]    # Blue
        test_image[50:, 50:] = [255, 255, 0]  # Yellow
        
        # Test method
        result = analyzer._calculate_color_clustering_variation(test_image)
        
        print("   - Testing color clustering results:")
        print(f"     ✅ Result type: {type(result)}")
        print(f"     ✅ Number of clusters: {result.get('num_clusters', 'N/A')}")
        print(f"     ✅ Cluster variation: {result.get('cluster_variation', 'N/A'):.3f}")
        print(f"     ✅ Clustering score: {result.get('clustering_score', 'N/A'):.3f}")
        print(f"     ✅ Dominant colors count: {len(result.get('dominant_colors', []))}")
        
        # Validate result structure
        required_keys = ['num_clusters', 'cluster_variation', 'dominant_colors', 'color_distribution', 'clustering_score']
        for key in required_keys:
            if key not in result:
                print(f"     ❌ Missing key: {key}")
                return False
            else:
                print(f"     ✅ Key present: {key}")
        
        print("✅ Color clustering method test completed successfully!")
        print("   - Method executes without errors")
        print("   - Returns proper data structure")
        print("   - Handles multi-color images correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Color clustering method test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_diameter_interpretation_method():
    """Test the diameter interpretation method"""
    print("🧪 Testing diameter interpretation method...")
    
    try:
        from core.abcde_analyzer import AdvancedABCDEAnalyzer
        
        # Create analyzer instance
        analyzer = AdvancedABCDEAnalyzer()
        
        # Test with different diameter scenarios
        test_cases = [
            {'diameter_pixels': 50, 'diameter_mm': 3.0, 'area_pixels': 1963},  # Small
            {'diameter_pixels': 100, 'diameter_mm': 6.0, 'area_pixels': 7854}, # Threshold
            {'diameter_pixels': 150, 'diameter_mm': 9.0, 'area_pixels': 17671} # Large
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"   - Testing case {i}: {test_case['diameter_mm']}mm")
            
            result = analyzer._interpret_diameter_score(test_case)
            
            print(f"     ✅ Result type: {type(result)}")
            print(f"     ✅ Score: {result.get('score', 'N/A'):.3f}")
            print(f"     ✅ Risk level: {result.get('risk_level', 'N/A')}")
            print(f"     ✅ Interpretation: {result.get('interpretation', 'N/A')[:50]}...")
            print(f"     ✅ Recommendations count: {len(result.get('recommendations', []))}")
            
            # Validate result structure
            required_keys = ['score', 'risk_level', 'interpretation', 'recommendations', 'confidence']
            for key in required_keys:
                if key not in result:
                    print(f"     ❌ Missing key: {key}")
                    return False
        
        print("✅ Diameter interpretation method test completed successfully!")
        print("   - Method executes without errors")
        print("   - Handles different diameter sizes correctly")
        print("   - Returns proper clinical interpretations")
        
        return True
        
    except Exception as e:
        print(f"❌ Diameter interpretation method test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_analysis_confidence_method():
    """Test the analysis confidence calculation method"""
    print("🧪 Testing analysis confidence method...")
    
    try:
        from core.abcde_analyzer import AdvancedABCDEAnalyzer
        
        # Create analyzer instance
        analyzer = AdvancedABCDEAnalyzer()
        
        # Create mock ABCDE results
        mock_abcde_results = {
            'asymmetry': {'score': 0.3, 'confidence': 0.8},
            'border': {'score': 0.5, 'confidence': 0.7},
            'color': {'overall_score': 0.4, 'confidence': 0.9},
            'diameter': {'score': 0.6, 'confidence': 0.8},
            'evolution': {'score': 0.2, 'confidence': 0.6}
        }
        
        # Test method
        result = analyzer._calculate_analysis_confidence(mock_abcde_results)
        
        print("   - Testing confidence calculation results:")
        print(f"     ✅ Result type: {type(result)}")
        print(f"     ✅ Overall confidence: {result.get('overall_confidence', 'N/A'):.3f}")
        print(f"     ✅ Analysis quality: {result.get('analysis_quality', 'N/A')}")
        print(f"     ✅ Quality score: {result.get('analysis_quality_score', 'N/A'):.3f}")
        print(f"     ✅ Reliability score: {result.get('reliability_score', 'N/A'):.3f}")
        print(f"     ✅ Components analyzed: {result.get('components_analyzed', 'N/A')}")
        print(f"     ✅ Completeness: {result.get('completeness_percentage', 'N/A'):.1f}%")
        
        # Validate result structure
        required_keys = [
            'overall_confidence', 'analysis_quality', 'analysis_quality_score',
            'reliability_score', 'components_analyzed', 'completeness_percentage',
            'confidence_breakdown', 'reliability_factors', 'quality_indicators'
        ]
        
        for key in required_keys:
            if key not in result:
                print(f"     ❌ Missing key: {key}")
                return False
            else:
                print(f"     ✅ Key present: {key}")
        
        # Test confidence breakdown
        breakdown = result.get('confidence_breakdown', {})
        expected_components = ['asymmetry', 'border', 'color', 'diameter', 'evolution']
        for component in expected_components:
            if component not in breakdown:
                print(f"     ❌ Missing confidence for: {component}")
                return False
        
        print("✅ Analysis confidence method test completed successfully!")
        print("   - Method executes without errors")
        print("   - Returns comprehensive confidence metrics")
        print("   - Includes all required components")
        
        return True
        
    except Exception as e:
        print(f"❌ Analysis confidence method test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run ABCDE methods fix tests"""
    print("🏥 DermatoGemma Multi-Detection System v2.0 - Ollama Edition")
    print("🧪 Testing ABCDE methods fixes...\n")
    
    tests = [
        ("Missing Methods Check", test_missing_methods),
        ("Color Clustering Method", test_color_clustering_method),
        ("Diameter Interpretation Method", test_diameter_interpretation_method),
        ("Analysis Confidence Method", test_analysis_confidence_method),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
        print()
    
    print(f"{'='*70}")
    print(f"🧪 ABCDE Methods Fix Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All ABCDE methods fix tests passed!")
        print("\n✨ ABCDE Issues Fixed:")
        print("   ✅ _calculate_color_clustering_variation implemented")
        print("   ✅ _interpret_diameter_score arguments fixed")
        print("   ✅ _calculate_analysis_confidence implemented")
        print("   ✅ All methods return proper data structures")
        print("   ✅ No more AttributeError or argument errors")
        print("\n💡 ABCDE analysis should now work without errors!")
        print("   Run 'python main.py' to test the full analysis.")
    else:
        print("⚠️ Some ABCDE methods fix tests failed")
        print("Check the specific test failures above")
    
    print(f"{'='*70}")

if __name__ == "__main__":
    main()
