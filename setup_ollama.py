#!/usr/bin/env python3
"""
Setup script for DermatoGemma Multi-Detection System v2.0 - Ollama Edition
Automates the setup process for Ollama integration
"""

import subprocess
import sys
import time
import requests
from pathlib import Path

def print_header():
    """Print setup header"""
    print("🏥 DermatoGemma Multi-Detection System v2.0 - Ollama Edition")
    print("🔧 Automated Setup Script")
    print("=" * 60)

def check_python():
    """Check Python version"""
    print("🐍 Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True

def check_ollama_installed():
    """Check if Ollama is installed"""
    print("🤖 Checking Ollama installation...")
    
    try:
        result = subprocess.run(['ollama', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ Ollama is installed")
            return True
        else:
            print("❌ Ollama command failed")
            return False
    except FileNotFoundError:
        print("❌ Ollama not found")
        return False
    except subprocess.TimeoutExpired:
        print("❌ Ollama command timeout")
        return False

def check_ollama_running():
    """Check if Ollama service is running"""
    print("🔗 Checking Ollama service...")
    
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama service is running")
            return True
        else:
            print("❌ Ollama service not responding")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Ollama service")
        return False
    except Exception as e:
        print(f"❌ Ollama service check failed: {e}")
        return False

def start_ollama_service():
    """Start Ollama service"""
    print("🚀 Starting Ollama service...")
    
    try:
        # Try to start Ollama in background
        subprocess.Popen(['ollama', 'serve'], 
                        stdout=subprocess.DEVNULL, 
                        stderr=subprocess.DEVNULL)
        
        # Wait a bit for service to start
        print("⏳ Waiting for Ollama service to start...")
        time.sleep(5)
        
        # Check if it's running now
        if check_ollama_running():
            return True
        else:
            print("❌ Failed to start Ollama service")
            return False
            
    except Exception as e:
        print(f"❌ Error starting Ollama service: {e}")
        return False

def check_model_available():
    """Check if gemma3n:e2b model is available"""
    print("🔍 Checking for gemma3n:e2b model...")
    
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json().get('models', [])
            model_names = [model['name'] for model in models]
            
            if any("gemma3n:e2b" in name for name in model_names):
                print("✅ gemma3n:e2b model is available")
                return True
            else:
                print("❌ gemma3n:e2b model not found")
                print(f"Available models: {model_names}")
                return False
        else:
            print("❌ Cannot check models")
            return False
    except Exception as e:
        print(f"❌ Model check failed: {e}")
        return False

def pull_model():
    """Pull gemma3n:e2b model"""
    print("📥 Pulling gemma3n:e2b model...")
    print("⚠️ This may take several minutes depending on your internet connection")
    
    try:
        result = subprocess.run(['ollama', 'pull', 'gemma3n:e2b'], 
                              capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ Model pulled successfully")
            return True
        else:
            print(f"❌ Model pull failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Model pull timeout (10 minutes)")
        return False
    except Exception as e:
        print(f"❌ Model pull error: {e}")
        return False

def install_requirements():
    """Install Python requirements"""
    print("📦 Installing Python requirements...")
    
    requirements_file = Path("requirements_ollama.txt")
    if not requirements_file.exists():
        print("❌ requirements_ollama.txt not found")
        return False
    
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements_ollama.txt'],
                              capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Requirements installed successfully")
            return True
        else:
            print(f"❌ Requirements installation failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Requirements installation timeout")
        return False
    except Exception as e:
        print(f"❌ Requirements installation error: {e}")
        return False

def run_test():
    """Run system test"""
    print("🧪 Running system test...")
    
    try:
        result = subprocess.run([sys.executable, 'test_simple.py'],
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ System test passed")
            print(result.stdout)
            return True
        else:
            print("❌ System test failed")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ System test timeout")
        return False
    except Exception as e:
        print(f"❌ System test error: {e}")
        return False

def main():
    """Main setup function"""
    print_header()
    
    steps = [
        ("Python Version", check_python),
        ("Ollama Installation", check_ollama_installed),
        ("Python Requirements", install_requirements),
        ("Ollama Service", check_ollama_running),
        ("Model Availability", check_model_available),
    ]
    
    # Run initial checks
    for step_name, step_func in steps:
        print(f"\n--- {step_name} ---")
        if not step_func():
            if step_name == "Ollama Installation":
                print("\n💡 Please install Ollama:")
                print("1. Visit: https://ollama.ai/")
                print("2. Download and install Ollama")
                print("3. Run this setup script again")
                return False
            elif step_name == "Ollama Service":
                print("🔄 Attempting to start Ollama service...")
                if not start_ollama_service():
                    print("\n💡 Please start Ollama manually:")
                    print("1. Open a terminal")
                    print("2. Run: ollama serve")
                    print("3. Run this setup script again")
                    return False
            elif step_name == "Model Availability":
                print("🔄 Attempting to pull model...")
                if not pull_model():
                    print("\n💡 Please pull the model manually:")
                    print("1. Ensure Ollama is running")
                    print("2. Run: ollama pull gemma3n:e2b")
                    print("3. Run this setup script again")
                    return False
            else:
                return False
    
    # Run final test
    print("\n--- Final System Test ---")
    if run_test():
        print("\n🎉 Setup completed successfully!")
        print("\n💡 You can now use the system:")
        print("python main.py")
    else:
        print("\n⚠️ Setup completed but system test failed")
        print("Please check the error messages above")
    
    return True

if __name__ == "__main__":
    main()
