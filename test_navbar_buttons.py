#!/usr/bin/env python3
"""
Test navbar buttons for DermatoGemma Multi-Detection System v2.0 - Ollama Edition
"""

# Import warning suppression module FIRST
import suppress_warnings

import sys
from pathlib import Path
import numpy as np
import tkinter as tk
import customtkinter as ctk

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_navbar_buttons_existence():
    """Test that navbar buttons exist and are properly configured"""
    print("🧪 Testing navbar buttons existence...")
    
    try:
        from ui_modern import ModernDermatoGemmaUI
        
        # Create GUI instance
        app = ModernDermatoGemmaUI()
        
        # Check if clear button exists
        if hasattr(app, 'clear_btn'):
            print("   ✅ Clear button exists")
            
            # Check button properties
            clear_text = app.clear_btn.cget("text")
            clear_state = app.clear_btn.cget("state")
            print(f"     - Text: {clear_text}")
            print(f"     - State: {clear_state}")
            
            if "Clear" in clear_text and "🧹" in clear_text:
                print("     ✅ Clear button text is correct")
            else:
                print("     ❌ Clear button text is incorrect")
                return False
                
        else:
            print("   ❌ Clear button missing")
            return False
        
        # Check if stop button exists
        if hasattr(app, 'stop_btn'):
            print("   ✅ Stop button exists")
            
            # Check button properties
            stop_text = app.stop_btn.cget("text")
            stop_state = app.stop_btn.cget("state")
            print(f"     - Text: {stop_text}")
            print(f"     - State: {stop_state}")
            
            if "Stop" in stop_text and "⏹️" in stop_text:
                print("     ✅ Stop button text is correct")
            else:
                print("     ❌ Stop button text is incorrect")
                return False
                
            if stop_state == "disabled":
                print("     ✅ Stop button initially disabled (correct)")
            else:
                print("     ❌ Stop button should be initially disabled")
                
        else:
            print("   ❌ Stop button missing")
            return False
        
        # Check if stop_requested variable exists
        if hasattr(app, 'stop_requested'):
            print("   ✅ stop_requested variable exists")
            print(f"     - Initial value: {app.stop_requested}")
        else:
            print("   ❌ stop_requested variable missing")
            return False
        
        print("✅ Navbar buttons existence test completed successfully!")
        print("   - Clear button properly configured")
        print("   - Stop button properly configured")
        print("   - Control variables exist")
        
        # Don't start mainloop for automated testing
        app.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Navbar buttons existence test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_clear_screens_functionality():
    """Test clear screens functionality"""
    print("🧪 Testing clear screens functionality...")
    
    try:
        from ui_modern import ModernDermatoGemmaUI
        
        # Create GUI instance
        app = ModernDermatoGemmaUI()
        
        # Set up some mock data to clear
        test_image = np.ones((200, 200, 3), dtype=np.uint8) * 128
        app.current_image = test_image
        app.analysis_results = {"test": "data"}
        app.processing = False
        
        # Enable some buttons to test reset
        app.analyze_btn.configure(state="normal")
        app.reset_btn.configure(state="normal")
        
        # Set analysis mode to single to test reset
        app.analysis_mode.set("single")
        app.condition_dropdown.configure(state="normal")
        app.selected_condition.set("Melanoma")
        
        print("   - Mock data set up:")
        print(f"     - Image: {app.current_image is not None}")
        print(f"     - Results: {app.analysis_results is not None}")
        print(f"     - Analysis mode: {app.analysis_mode.get()}")
        print(f"     - Selected condition: {app.selected_condition.get()}")
        
        # Test clear_all_screens method
        if hasattr(app, 'clear_all_screens'):
            print("   ✅ clear_all_screens method exists")
            
            # Call clear method
            app.clear_all_screens()
            
            # Check if data was cleared
            print("   - After clearing:")
            print(f"     - Image cleared: {app.current_image is None}")
            print(f"     - Results cleared: {app.analysis_results is None}")
            print(f"     - Analysis mode reset: {app.analysis_mode.get() == 'multi'}")
            print(f"     - Condition dropdown disabled: {app.condition_dropdown.cget('state') == 'disabled'}")
            print(f"     - Processing flag reset: {app.processing == False}")
            
            # Validate clearing
            if (app.current_image is None and 
                app.analysis_results is None and 
                app.analysis_mode.get() == "multi" and
                not app.processing):
                print("   ✅ Clear screens functionality works correctly")
            else:
                print("   ❌ Clear screens functionality failed")
                return False
                
        else:
            print("   ❌ clear_all_screens method missing")
            return False
        
        print("✅ Clear screens functionality test completed successfully!")
        print("   - All data properly cleared")
        print("   - Interface reset to initial state")
        print("   - Button states properly reset")
        
        # Don't start mainloop for automated testing
        app.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Clear screens functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_stop_analysis_functionality():
    """Test stop analysis functionality"""
    print("🧪 Testing stop analysis functionality...")
    
    try:
        from ui_modern import ModernDermatoGemmaUI
        
        # Create GUI instance
        app = ModernDermatoGemmaUI()
        
        # Test stop_analysis method exists
        if hasattr(app, 'stop_analysis'):
            print("   ✅ stop_analysis method exists")
            
            # Test stopping when no analysis is running
            print("   - Testing stop when no analysis running:")
            app.processing = False
            app.stop_analysis()
            print(f"     - Stop requested: {app.stop_requested}")
            
            # Test stopping when analysis is running
            print("   - Testing stop when analysis is running:")
            app.processing = True
            app.stop_requested = False
            app.analyze_btn.configure(state="disabled", text="🔄 Processing...")
            app.stop_btn.configure(state="normal")
            
            # Call stop method
            app.stop_analysis()
            
            print(f"     - Processing stopped: {not app.processing}")
            print(f"     - Stop requested: {app.stop_requested}")
            print(f"     - Analyze button enabled: {app.analyze_btn.cget('state') == 'normal'}")
            print(f"     - Stop button disabled: {app.stop_btn.cget('state') == 'disabled'}")
            
            # Validate stop functionality
            if (not app.processing and 
                app.stop_requested and
                app.analyze_btn.cget('state') == 'normal' and
                app.stop_btn.cget('state') == 'disabled'):
                print("   ✅ Stop analysis functionality works correctly")
            else:
                print("   ❌ Stop analysis functionality failed")
                return False
                
        else:
            print("   ❌ stop_analysis method missing")
            return False
        
        print("✅ Stop analysis functionality test completed successfully!")
        print("   - Stop method properly implemented")
        print("   - Processing flags correctly managed")
        print("   - Button states properly updated")
        
        # Don't start mainloop for automated testing
        app.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Stop analysis functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_button_integration():
    """Test button integration with existing functionality"""
    print("🧪 Testing button integration...")
    
    try:
        from ui_modern import ModernDermatoGemmaUI
        
        # Create GUI instance
        app = ModernDermatoGemmaUI()
        
        # Test that buttons don't interfere with existing functionality
        print("   - Testing integration with existing buttons:")
        
        # Check that all buttons exist
        buttons_to_check = ['clear_btn', 'stop_btn', 'analyze_btn', 'reset_btn']
        for button_name in buttons_to_check:
            if hasattr(app, button_name):
                button = getattr(app, button_name)
                print(f"     ✅ {button_name}: exists")
                print(f"       - Text: {button.cget('text')}")
                print(f"       - State: {button.cget('state')}")
            else:
                print(f"     ❌ {button_name}: missing")
                return False
        
        # Test button state coordination
        print("   - Testing button state coordination:")
        
        # Simulate image loading (should enable some buttons)
        test_image = np.ones((200, 200, 3), dtype=np.uint8) * 128
        app.current_image = test_image
        app.analyze_btn.configure(state="normal")
        app.reset_btn.configure(state="normal")
        
        print(f"     - After image load:")
        print(f"       - Analyze: {app.analyze_btn.cget('state')}")
        print(f"       - Reset: {app.reset_btn.cget('state')}")
        print(f"       - Clear: {app.clear_btn.cget('state')}")
        print(f"       - Stop: {app.stop_btn.cget('state')}")
        
        # Simulate analysis start (should change button states)
        app.processing = True
        app.analyze_btn.configure(state="disabled", text="🔄 Processing...")
        app.stop_btn.configure(state="normal")
        
        print(f"     - During analysis:")
        print(f"       - Analyze: {app.analyze_btn.cget('state')}")
        print(f"       - Stop: {app.stop_btn.cget('state')}")
        
        # Test clear functionality doesn't break other buttons
        app.clear_all_screens()
        
        print(f"     - After clear:")
        print(f"       - Analyze: {app.analyze_btn.cget('state')}")
        print(f"       - Reset: {app.reset_btn.cget('state')}")
        print(f"       - Stop: {app.stop_btn.cget('state')}")
        
        print("✅ Button integration test completed successfully!")
        print("   - All buttons exist and are properly configured")
        print("   - Button states coordinate correctly")
        print("   - No interference with existing functionality")
        
        # Don't start mainloop for automated testing
        app.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Button integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run navbar buttons tests"""
    print("🏥 DermatoGemma Multi-Detection System v2.0 - Ollama Edition")
    print("🧪 Testing navbar buttons functionality...\n")
    
    tests = [
        ("Navbar Buttons Existence", test_navbar_buttons_existence),
        ("Clear Screens Functionality", test_clear_screens_functionality),
        ("Stop Analysis Functionality", test_stop_analysis_functionality),
        ("Button Integration", test_button_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
        print()
    
    print(f"{'='*70}")
    print(f"🧪 Navbar Buttons Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All navbar buttons tests passed!")
        print("\n✨ Navbar Features Implemented:")
        print("   ✅ Clear Screens button in navbar")
        print("   ✅ Stop Analysis button in navbar")
        print("   ✅ Clear functionality resets entire interface")
        print("   ✅ Stop functionality halts running analysis")
        print("   ✅ Button states coordinate properly")
        print("   ✅ Integration with existing buttons works")
        print("\n🎯 Navbar Controls:")
        print("   🧹 Clear Screens - Resets interface to initial state")
        print("   ⏹️ Stop Analysis - Halts current analysis process")
        print("   📊 Dynamic states - Enabled/disabled based on context")
        print("\n💡 The navbar now provides quick access to essential controls!")
        print("   Run 'python main.py' to test the navbar buttons.")
    else:
        print("⚠️ Some navbar buttons tests failed")
        print("Check the specific test failures above")
    
    print(f"{'='*70}")

if __name__ == "__main__":
    main()
