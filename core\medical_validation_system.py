"""
DermatoGemma Multi-Detection System v2.0
REAL Medical Validation System

Comprehensive medical validation system for dermatological analysis results
with clinical guidelines compliance and professional medical standards.
"""

import numpy as np
import logging
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime
from pathlib import Path
import json

# Import our medical database
import sys
sys.path.append(str(Path(__file__).parent.parent))
from data.medical_reference_database import medical_db

logger = logging.getLogger(__name__)

class MedicalValidationSystem:
    """
    REAL medical validation system for dermatological analysis
    Ensures clinical accuracy and compliance with medical guidelines
    """
    
    def __init__(self):
        self.medical_db = medical_db
        self.validation_criteria = self._initialize_validation_criteria()
        self.clinical_thresholds = self._initialize_clinical_thresholds()
        self.validation_history = []
        
        logger.info("✅ Medical Validation System initialized")
    
    def _initialize_validation_criteria(self) -> Dict[str, Any]:
        """Initialize medical validation criteria"""
        return {
            'confidence_thresholds': {
                'critical_conditions': 0.85,  # <PERSON>, SCC, BCC
                'precancerous_conditions': 0.75,  # Atypical nevus, AK
                'benign_conditions': 0.65  # SK, dermatofibroma, hemangioma
            },
            'abcde_validation': {
                'melanoma_minimum_score': 0.6,
                'atypical_nevus_minimum_score': 0.4,
                'required_features': ['asymmetry', 'border', 'color']
            },
            'clinical_consistency': {
                'feature_correlation_threshold': 0.7,
                'multi_condition_agreement_threshold': 0.8
            },
            'quality_metrics': {
                'minimum_image_quality': 0.7,
                'minimum_lesion_size_pixels': 100,
                'maximum_analysis_time_seconds': 30
            }
        }
    
    def _initialize_clinical_thresholds(self) -> Dict[str, Dict[str, float]]:
        """Initialize clinical decision thresholds"""
        return {
            'melanoma': {
                'asymmetry_threshold': 0.5,
                'border_irregularity_threshold': 0.4,
                'color_variation_threshold': 0.6,
                'diameter_threshold_mm': 6.0,
                'evolution_significance': 0.3
            },
            'basal_cell_carcinoma': {
                'border_definition_threshold': 0.3,
                'telangiectasia_presence': 0.4,
                'pearly_appearance': 0.5,
                'ulceration_threshold': 0.2
            },
            'squamous_cell_carcinoma': {
                'keratotic_surface_threshold': 0.4,
                'induration_threshold': 0.3,
                'rapid_growth_indicator': 0.5,
                'ulceration_threshold': 0.3
            }
        }
    
    def validate_analysis_results(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        REAL medical validation of analysis results
        
        Args:
            analysis_results: Complete analysis results from multi-condition engine
            
        Returns:
            Comprehensive validation report with clinical recommendations
        """
        try:
            logger.info("🏥 Starting REAL medical validation...")
            
            validation_report = {
                'validation_timestamp': datetime.now().isoformat(),
                'overall_validity': False,
                'clinical_confidence': 0.0,
                'medical_recommendations': [],
                'validation_details': {},
                'quality_assessment': {},
                'clinical_alerts': [],
                'follow_up_required': False
            }
            
            # 1. Validate input data quality
            quality_validation = self._validate_data_quality(analysis_results)
            validation_report['quality_assessment'] = quality_validation
            
            if not quality_validation['acceptable_quality']:
                validation_report['clinical_alerts'].append({
                    'level': 'warning',
                    'message': 'Image quality may affect diagnostic accuracy',
                    'recommendation': 'Consider retaking image with better lighting/focus'
                })
            
            # 2. Validate ABCDE analysis for melanoma screening
            abcde_validation = self._validate_abcde_analysis(analysis_results)
            validation_report['validation_details']['abcde'] = abcde_validation
            
            # 3. Validate multi-condition analysis consistency
            consistency_validation = self._validate_multi_condition_consistency(analysis_results)
            validation_report['validation_details']['consistency'] = consistency_validation
            
            # 4. Clinical risk assessment
            risk_assessment = self._perform_clinical_risk_assessment(analysis_results)
            validation_report['validation_details']['risk_assessment'] = risk_assessment
            
            # 5. Generate medical recommendations
            medical_recommendations = self._generate_medical_recommendations(analysis_results, risk_assessment)
            validation_report['medical_recommendations'] = medical_recommendations
            
            # 6. Determine overall clinical confidence
            clinical_confidence = self._calculate_clinical_confidence(
                quality_validation, abcde_validation, consistency_validation, risk_assessment
            )
            validation_report['clinical_confidence'] = clinical_confidence
            
            # 7. Determine if follow-up is required
            validation_report['follow_up_required'] = self._requires_medical_follow_up(risk_assessment)
            
            # 8. Final validation status
            validation_report['overall_validity'] = (
                quality_validation['acceptable_quality'] and
                clinical_confidence >= 0.7 and
                consistency_validation['consistent']
            )
            
            # Store validation history
            self.validation_history.append(validation_report)
            
            logger.info(f"✅ Medical validation completed - Confidence: {clinical_confidence:.2f}")
            return validation_report
            
        except Exception as e:
            logger.error(f"❌ Medical validation failed: {e}")
            return {
                'validation_timestamp': datetime.now().isoformat(),
                'overall_validity': False,
                'clinical_confidence': 0.0,
                'error': str(e),
                'medical_recommendations': [
                    {
                        'priority': 'high',
                        'recommendation': 'Manual dermatologist review required due to validation failure'
                    }
                ]
            }
    
    def _validate_data_quality(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Validate the quality of input data and analysis"""
        try:
            quality_metrics = {
                'acceptable_quality': True,
                'quality_score': 1.0,
                'issues': []
            }
            
            # Check if lesions were detected
            lesions = analysis_results.get('lesions_detected', [])
            if not lesions:
                quality_metrics['issues'].append('No lesions detected in image')
                quality_metrics['quality_score'] *= 0.5
            
            # Check lesion size adequacy
            for lesion in lesions:
                area = lesion.get('area', 0)
                if area < self.validation_criteria['quality_metrics']['minimum_lesion_size_pixels']:
                    quality_metrics['issues'].append(f'Lesion too small for reliable analysis (area: {area})')
                    quality_metrics['quality_score'] *= 0.8
            
            # Check analysis completeness
            required_analyses = ['abcde_analysis', 'multi_condition_analysis']
            for analysis_type in required_analyses:
                if analysis_type not in analysis_results:
                    quality_metrics['issues'].append(f'Missing {analysis_type}')
                    quality_metrics['quality_score'] *= 0.7
            
            # Determine if quality is acceptable
            quality_metrics['acceptable_quality'] = (
                quality_metrics['quality_score'] >= self.validation_criteria['quality_metrics']['minimum_image_quality']
            )
            
            return quality_metrics
            
        except Exception as e:
            logger.error(f"Data quality validation failed: {e}")
            return {
                'acceptable_quality': False,
                'quality_score': 0.0,
                'issues': [f'Quality validation error: {str(e)}']
            }
    
    def _validate_abcde_analysis(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Validate ABCDE analysis against clinical criteria"""
        try:
            abcde_validation = {
                'valid': True,
                'clinical_significance': 'low',
                'melanoma_risk_indicators': [],
                'abcde_scores': {},
                'clinical_interpretation': ''
            }
            
            abcde_results = analysis_results.get('abcde_analysis', {})
            if not abcde_results:
                abcde_validation['valid'] = False
                abcde_validation['clinical_interpretation'] = 'ABCDE analysis not performed'
                return abcde_validation
            
            # Extract ABCDE scores
            abcde_scores = {
                'asymmetry': abcde_results.get('asymmetry_score', 0.0),
                'border': abcde_results.get('border_irregularity_score', 0.0),
                'color': abcde_results.get('color_variation_score', 0.0),
                'diameter': abcde_results.get('diameter_score', 0.0),
                'evolution': abcde_results.get('evolution_score', 0.0)
            }
            abcde_validation['abcde_scores'] = abcde_scores
            
            # Check against clinical thresholds
            melanoma_thresholds = self.clinical_thresholds.get('melanoma', {})
            risk_indicators = []
            
            if abcde_scores['asymmetry'] >= melanoma_thresholds.get('asymmetry_threshold', 0.5):
                risk_indicators.append('Significant asymmetry detected')
            
            if abcde_scores['border'] >= melanoma_thresholds.get('border_irregularity_threshold', 0.4):
                risk_indicators.append('Irregular border pattern')
            
            if abcde_scores['color'] >= melanoma_thresholds.get('color_variation_threshold', 0.6):
                risk_indicators.append('Multiple colors present')
            
            diameter_mm = abcde_results.get('diameter_mm', 0.0)
            if diameter_mm >= melanoma_thresholds.get('diameter_threshold_mm', 6.0):
                risk_indicators.append(f'Large diameter ({diameter_mm:.1f}mm)')
            
            if abcde_scores['evolution'] >= melanoma_thresholds.get('evolution_significance', 0.3):
                risk_indicators.append('Evidence of recent changes')
            
            abcde_validation['melanoma_risk_indicators'] = risk_indicators
            
            # Determine clinical significance
            total_risk_score = sum(abcde_scores.values()) / len(abcde_scores)
            if total_risk_score >= 0.7:
                abcde_validation['clinical_significance'] = 'high'
                abcde_validation['clinical_interpretation'] = 'High melanoma risk - urgent dermatologist referral recommended'
            elif total_risk_score >= 0.5:
                abcde_validation['clinical_significance'] = 'moderate'
                abcde_validation['clinical_interpretation'] = 'Moderate melanoma risk - dermatologist evaluation recommended'
            elif total_risk_score >= 0.3:
                abcde_validation['clinical_significance'] = 'low_moderate'
                abcde_validation['clinical_interpretation'] = 'Some concerning features - monitoring recommended'
            else:
                abcde_validation['clinical_significance'] = 'low'
                abcde_validation['clinical_interpretation'] = 'Low melanoma risk - routine monitoring'
            
            return abcde_validation
            
        except Exception as e:
            logger.error(f"ABCDE validation failed: {e}")
            return {
                'valid': False,
                'clinical_significance': 'unknown',
                'error': str(e)
            }
    
    def _validate_multi_condition_consistency(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Validate consistency across multi-condition analysis"""
        try:
            consistency_validation = {
                'consistent': True,
                'consistency_score': 1.0,
                'conflicts': [],
                'primary_diagnosis_confidence': 0.0
            }
            
            multi_condition_results = analysis_results.get('multi_condition_analysis', {})
            if not multi_condition_results:
                consistency_validation['consistent'] = False
                consistency_validation['conflicts'].append('Multi-condition analysis missing')
                return consistency_validation
            
            # Check for conflicting diagnoses
            condition_scores = {}
            for condition, result in multi_condition_results.items():
                if isinstance(result, dict) and 'confidence' in result:
                    condition_scores[condition] = result['confidence']
            
            if condition_scores:
                # Find primary diagnosis
                primary_condition = max(condition_scores, key=condition_scores.get)
                primary_confidence = condition_scores[primary_condition]
                consistency_validation['primary_diagnosis_confidence'] = primary_confidence
                
                # Check for competing high-confidence diagnoses
                high_confidence_conditions = [
                    cond for cond, conf in condition_scores.items() 
                    if conf >= 0.7 and cond != primary_condition
                ]
                
                if len(high_confidence_conditions) > 0:
                    consistency_validation['conflicts'].append(
                        f'Multiple high-confidence diagnoses: {primary_condition} ({primary_confidence:.2f}) vs {high_confidence_conditions}'
                    )
                    consistency_validation['consistency_score'] *= 0.7
                
                # Check clinical logic consistency
                malignant_conditions = ['melanoma', 'basal_cell_carcinoma', 'squamous_cell_carcinoma']
                benign_conditions = ['seborrheic_keratosis', 'dermatofibroma', 'hemangioma']
                
                malignant_total = sum(condition_scores.get(cond, 0) for cond in malignant_conditions)
                benign_total = sum(condition_scores.get(cond, 0) for cond in benign_conditions)
                
                if malignant_total > 0.5 and benign_total > 0.5:
                    consistency_validation['conflicts'].append(
                        'Conflicting malignant vs benign assessments'
                    )
                    consistency_validation['consistency_score'] *= 0.6
            
            # Final consistency determination
            consistency_validation['consistent'] = (
                consistency_validation['consistency_score'] >= 
                self.validation_criteria['clinical_consistency']['multi_condition_agreement_threshold']
            )
            
            return consistency_validation
            
        except Exception as e:
            logger.error(f"Consistency validation failed: {e}")
            return {
                'consistent': False,
                'consistency_score': 0.0,
                'error': str(e)
            }
    
    def _perform_clinical_risk_assessment(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Perform comprehensive clinical risk assessment"""
        try:
            risk_assessment = {
                'overall_risk_level': 'low',
                'malignancy_probability': 0.0,
                'urgency_level': 'routine',
                'recommended_action': 'monitor',
                'risk_factors': [],
                'protective_factors': []
            }
            
            # Analyze multi-condition results for risk
            multi_condition_results = analysis_results.get('multi_condition_analysis', {})
            
            malignant_risk = 0.0
            precancerous_risk = 0.0
            
            for condition, result in multi_condition_results.items():
                if isinstance(result, dict) and 'confidence' in result:
                    confidence = result['confidence']
                    condition_data = self.medical_db.get_condition_data(condition)
                    
                    if condition_data.get('classification') == 'malignant':
                        malignant_risk = max(malignant_risk, confidence)
                    elif condition_data.get('classification') == 'precancerous':
                        precancerous_risk = max(precancerous_risk, confidence)
            
            # Calculate overall malignancy probability
            risk_assessment['malignancy_probability'] = malignant_risk + (0.3 * precancerous_risk)
            
            # Determine risk level and actions
            if malignant_risk >= 0.8:
                risk_assessment['overall_risk_level'] = 'critical'
                risk_assessment['urgency_level'] = 'immediate'
                risk_assessment['recommended_action'] = 'urgent_referral'
            elif malignant_risk >= 0.6 or precancerous_risk >= 0.8:
                risk_assessment['overall_risk_level'] = 'high'
                risk_assessment['urgency_level'] = 'urgent'
                risk_assessment['recommended_action'] = 'dermatologist_referral'
            elif malignant_risk >= 0.4 or precancerous_risk >= 0.6:
                risk_assessment['overall_risk_level'] = 'moderate'
                risk_assessment['urgency_level'] = 'routine'
                risk_assessment['recommended_action'] = 'close_monitoring'
            else:
                risk_assessment['overall_risk_level'] = 'low'
                risk_assessment['urgency_level'] = 'routine'
                risk_assessment['recommended_action'] = 'routine_monitoring'
            
            return risk_assessment
            
        except Exception as e:
            logger.error(f"Risk assessment failed: {e}")
            return {
                'overall_risk_level': 'unknown',
                'malignancy_probability': 0.0,
                'error': str(e)
            }
    
    def _generate_medical_recommendations(self, analysis_results: Dict[str, Any], risk_assessment: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate evidence-based medical recommendations"""
        recommendations = []
        
        risk_level = risk_assessment.get('overall_risk_level', 'low')
        
        if risk_level == 'critical':
            recommendations.append({
                'priority': 'urgent',
                'timeframe': '1-2 weeks',
                'action': 'Immediate dermatologist referral',
                'rationale': 'High suspicion of malignant melanoma or aggressive skin cancer'
            })
        elif risk_level == 'high':
            recommendations.append({
                'priority': 'high',
                'timeframe': '2-4 weeks',
                'action': 'Dermatologist evaluation',
                'rationale': 'Concerning features requiring specialist assessment'
            })
        elif risk_level == 'moderate':
            recommendations.append({
                'priority': 'moderate',
                'timeframe': '6-12 weeks',
                'action': 'Close monitoring or dermatologist consultation',
                'rationale': 'Some concerning features present'
            })
        else:
            recommendations.append({
                'priority': 'routine',
                'timeframe': '6-12 months',
                'action': 'Routine skin examination',
                'rationale': 'Low risk lesion, routine monitoring appropriate'
            })
        
        # Add general recommendations
        recommendations.append({
            'priority': 'general',
            'action': 'Sun protection measures',
            'rationale': 'Prevention of future skin damage'
        })
        
        recommendations.append({
            'priority': 'general',
            'action': 'Regular self-examination',
            'rationale': 'Early detection of changes'
        })
        
        return recommendations
    
    def _calculate_clinical_confidence(self, quality_validation: Dict, abcde_validation: Dict, 
                                     consistency_validation: Dict, risk_assessment: Dict) -> float:
        """Calculate overall clinical confidence in the analysis"""
        try:
            confidence_factors = []
            
            # Quality factor
            confidence_factors.append(quality_validation.get('quality_score', 0.0) * 0.3)
            
            # ABCDE validity factor
            if abcde_validation.get('valid', False):
                confidence_factors.append(0.25)
            
            # Consistency factor
            confidence_factors.append(consistency_validation.get('consistency_score', 0.0) * 0.25)
            
            # Risk assessment confidence
            if 'error' not in risk_assessment:
                confidence_factors.append(0.2)
            
            return sum(confidence_factors)
            
        except Exception as e:
            logger.error(f"Clinical confidence calculation failed: {e}")
            return 0.0
    
    def _requires_medical_follow_up(self, risk_assessment: Dict[str, Any]) -> bool:
        """Determine if medical follow-up is required"""
        risk_level = risk_assessment.get('overall_risk_level', 'low')
        return risk_level in ['critical', 'high', 'moderate']

# Global instance
medical_validator = MedicalValidationSystem()
