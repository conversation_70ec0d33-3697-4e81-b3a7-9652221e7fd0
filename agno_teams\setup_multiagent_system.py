#!/usr/bin/env python3
"""
🔧 Setup Script for DermatoGemma Multi-Agent System
Script de configuração e instalação do sistema de multiagentes
"""

import os
import sys
import subprocess
import logging
from pathlib import Path
from typing import List, Dict, Any

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MultiAgentSystemSetup:
    """Setup and configuration manager for the multi-agent system"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.agno_teams_dir = Path(__file__).parent
        self.requirements_file = self.agno_teams_dir / "requirements.txt"
        
    def check_python_version(self) -> bool:
        """Check if Python version is compatible"""
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            logger.error("❌ Python 3.8+ is required")
            return False
        
        logger.info(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
        return True
    
    def install_requirements(self) -> bool:
        """Install required packages"""
        try:
            logger.info("📦 Installing Agno Teams and dependencies...")
            
            # Install requirements
            cmd = [sys.executable, "-m", "pip", "install", "-r", str(self.requirements_file)]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"❌ Failed to install requirements: {result.stderr}")
                return False
            
            logger.info("✅ All requirements installed successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Installation failed: {e}")
            return False
    
    def setup_directories(self) -> bool:
        """Create necessary directories"""
        try:
            directories = [
                self.agno_teams_dir / "tmp",
                self.agno_teams_dir / "logs", 
                self.agno_teams_dir / "data",
                self.project_root / "test_images" / "melanoma",
                self.project_root / "test_images" / "benign",
                self.project_root / "test_images" / "other"
            ]
            
            for directory in directories:
                directory.mkdir(parents=True, exist_ok=True)
                logger.info(f"📁 Created directory: {directory}")
            
            logger.info("✅ All directories created successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Directory creation failed: {e}")
            return False
    
    def create_environment_file(self) -> bool:
        """Create .env file with configuration"""
        try:
            env_file = self.agno_teams_dir / ".env"
            
            env_content = """# DermatoGemma Multi-Agent System Configuration

# Model Configuration
USE_OPENAI=false
OPENAI_API_KEY=your_openai_api_key_here
OLLAMA_HOST=http://localhost:11434
OLLAMA_MODEL=gemma3n:e4b

# Environment
DERMATOGAMMA_ENV=development

# Logging
LOG_LEVEL=INFO

# Database
MEMORY_DB_PATH=agno_teams/dermatology_memory.db
KNOWLEDGE_DB_PATH=agno_teams/tmp/medical_lancedb

# Timeouts (in seconds)
CONNECTION_TIMEOUT=3600
ANALYSIS_TIMEOUT=3600
MODEL_TEST_TIMEOUT=1800
"""
            
            with open(env_file, "w") as f:
                f.write(env_content)
            
            logger.info(f"✅ Environment file created: {env_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Environment file creation failed: {e}")
            return False
    
    def check_ollama_installation(self) -> bool:
        """Check if Ollama is installed and running"""
        try:
            import requests
            
            # Check if Ollama is running
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            
            if response.status_code == 200:
                models = response.json().get("models", [])
                model_names = [model["name"] for model in models]
                
                logger.info(f"✅ Ollama is running with models: {model_names}")
                
                # Check if Gemma model is available
                if any("gemma" in name.lower() for name in model_names):
                    logger.info("✅ Gemma model detected")
                else:
                    logger.warning("⚠️ Gemma model not found. You may need to pull it:")
                    logger.warning("   ollama pull gemma3n:e4b")
                
                return True
            else:
                logger.warning("⚠️ Ollama is not responding properly")
                return False
                
        except ImportError:
            logger.warning("⚠️ requests library not available for Ollama check")
            return False
        except Exception as e:
            logger.warning(f"⚠️ Could not connect to Ollama: {e}")
            logger.info("💡 Make sure Ollama is installed and running:")
            logger.info("   1. Install Ollama from https://ollama.ai")
            logger.info("   2. Run: ollama serve")
            logger.info("   3. Pull model: ollama pull gemma3n:e4b")
            return False
    
    def create_test_script(self) -> bool:
        """Create a test script to verify installation"""
        try:
            test_script = self.agno_teams_dir / "test_installation.py"
            
            test_content = '''#!/usr/bin/env python3
"""Test script to verify DermatoGemma Multi-Agent System installation"""

import sys
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_imports():
    """Test if all required modules can be imported"""
    try:
        # Test Agno Teams
        from agno.agent import Agent
        from agno.team import Team
        logger.info("✅ Agno Teams imported successfully")
        
        # Test our modules
        from config import get_config
        from dermatology_multiagent_system import DermatologyMultiAgentSystem
        logger.info("✅ DermatoGemma modules imported successfully")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Import failed: {e}")
        return False

def test_configuration():
    """Test configuration loading"""
    try:
        from config import get_config
        config = get_config()
        
        # Validate configuration
        issues = config.validate_config()
        if issues:
            logger.warning(f"⚠️ Configuration issues: {issues}")
        else:
            logger.info("✅ Configuration validated successfully")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration test failed: {e}")
        return False

def test_system_initialization():
    """Test multi-agent system initialization"""
    try:
        from dermatology_multiagent_system import DermatologyMultiAgentSystem
        
        # Initialize with Ollama (safer for testing)
        system = DermatologyMultiAgentSystem(use_openai=False)
        logger.info("✅ Multi-agent system initialized successfully")
        
        # Test metrics
        metrics = system.get_team_metrics()
        logger.info(f"📊 System metrics: {metrics}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ System initialization failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing DermatoGemma Multi-Agent System Installation")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("Configuration Test", test_configuration),
        ("System Initialization Test", test_system_initialization)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\\n🔍 Running {test_name}...")
        if test_func():
            passed += 1
        
    print(f"\\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready for use.")
        return True
    else:
        print("❌ Some tests failed. Please check the logs above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
'''
            
            with open(test_script, "w") as f:
                f.write(test_content)
            
            # Make executable
            test_script.chmod(0o755)
            
            logger.info(f"✅ Test script created: {test_script}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Test script creation failed: {e}")
            return False
    
    def display_setup_summary(self):
        """Display setup completion summary"""
        print("\n" + "="*80)
        print("🎉 DERMATOGAMMA MULTI-AGENT SYSTEM SETUP COMPLETE")
        print("="*80)
        
        print("\n📁 Project Structure:")
        print("   agno_teams/")
        print("   ├── dermatology_multiagent_system.py  # Main multi-agent system")
        print("   ├── config.py                         # Configuration management")
        print("   ├── demo_multiagent_analysis.py       # Demonstration script")
        print("   ├── requirements.txt                   # Dependencies")
        print("   ├── .env                              # Environment variables")
        print("   ├── test_installation.py             # Installation test")
        print("   └── logs/                             # Log files")
        
        print("\n🚀 Next Steps:")
        print("   1. Test the installation:")
        print("      cd agno_teams && python test_installation.py")
        print("   2. Run the demonstration:")
        print("      python demo_multiagent_analysis.py")
        print("   3. Configure your environment in .env file")
        print("   4. Start using the multi-agent system!")
        
        print("\n💡 Usage Examples:")
        print("   # Initialize the system")
        print("   from dermatology_multiagent_system import DermatologyMultiAgentSystem")
        print("   system = DermatologyMultiAgentSystem(use_openai=False)")
        print("   ")
        print("   # Analyze a skin lesion")
        print("   result = system.analyze_skin_lesion('path/to/image.jpg')")
        print("   ")
        print("   # High-risk analysis")
        print("   result = system.analyze_high_risk_lesion('path/to/image.jpg')")
        
        print("\n🔧 Configuration:")
        print("   • Edit agno_teams/.env for your settings")
        print("   • Set USE_OPENAI=true if you have OpenAI API key")
        print("   • Ensure Ollama is running for local models")
        
    def run_setup(self) -> bool:
        """Run the complete setup process"""
        print("🔧 DermatoGemma Multi-Agent System Setup")
        print("=" * 50)
        
        steps = [
            ("Checking Python version", self.check_python_version),
            ("Setting up directories", self.setup_directories),
            ("Installing requirements", self.install_requirements),
            ("Creating environment file", self.create_environment_file),
            ("Checking Ollama installation", self.check_ollama_installation),
            ("Creating test script", self.create_test_script)
        ]
        
        for step_name, step_func in steps:
            logger.info(f"🔄 {step_name}...")
            if not step_func():
                logger.error(f"❌ Setup failed at: {step_name}")
                return False
        
        self.display_setup_summary()
        return True

def main():
    """Main setup function"""
    setup = MultiAgentSystemSetup()
    success = setup.run_setup()
    
    if success:
        print("\n✅ Setup completed successfully!")
        return 0
    else:
        print("\n❌ Setup failed. Please check the logs above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
