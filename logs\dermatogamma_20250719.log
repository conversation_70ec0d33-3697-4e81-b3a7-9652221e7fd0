2025-07-19 18:05:18,586 - __main__ - ERROR - Please install missing dependencies with: pip install -r requirements.txt
2025-07-19 18:06:49,201 - __main__ - INFO - DermatoGemma Multi-Detection System v2.0 initializing...
2025-07-19 18:06:49,519 - __main__ - INFO - Initializing application components...
2025-07-19 18:06:49,519 - __main__ - INFO - DermatoGemma Multi-Detection System v2.0 starting...
2025-07-19 18:06:49,519 - __main__ - INFO - Checking system requirements...
2025-07-19 18:06:49,519 - __main__ - INFO - Python version: 3.12.7
2025-07-19 18:06:49,708 - __main__ - INFO - numpy available
2025-07-19 18:06:49,943 - __main__ - INFO - opencv-python available
2025-07-19 18:06:50,512 - __main__ - INFO - PIL available
2025-07-19 18:06:50,512 - __main__ - INFO - tkinter available
2025-07-19 18:06:50,734 - __main__ - INFO - matplotlib available
2025-07-19 18:07:01,632 - __main__ - INFO - scikit-learn available
2025-07-19 18:07:02,232 - __main__ - INFO - scikit-image available
2025-07-19 18:07:20,430 - __main__ - INFO - torch available (AI features enabled)
2025-07-19 18:07:27,818 - __main__ - INFO - transformers available (AI features enabled)
2025-07-19 18:08:04,793 - numexpr.utils - INFO - NumExpr defaulting to 4 threads.
2025-07-19 18:08:37,840 - __main__ - INFO - mediapipe available (AI features enabled)
2025-07-19 18:08:37,840 - __main__ - INFO - Directory models ready
2025-07-19 18:08:37,840 - __main__ - INFO - Directory data ready
2025-07-19 18:08:37,840 - __main__ - INFO - Directory results ready
2025-07-19 18:08:37,840 - __main__ - INFO - Directory logs ready
2025-07-19 18:08:37,840 - __main__ - INFO - System requirements check completed
2025-07-19 18:08:37,840 - __main__ - INFO - Initializing core analysis components...
