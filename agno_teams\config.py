#!/usr/bin/env python3
"""
🔧 Configuration for DermatoGemma Multi-Agent System
Configurações centralizadas para o sistema de multiagentes
"""

import os
from pathlib import Path
from typing import Dict, Any, List

class AgnoTeamsConfig:
    """Configuration class for Agno Teams setup"""
    
    # Model Configuration
    USE_OPENAI = os.getenv("USE_OPENAI", "false").lower() == "true"
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    OLLAMA_HOST = os.getenv("OLLAMA_HOST", "http://localhost:11434")
    OLLAMA_MODEL = os.getenv("OLLAMA_MODEL", "gemma3n:e4b")
    
    # OpenAI Models
    OPENAI_CHAT_MODEL = "gpt-4o"
    OPENAI_EMBEDDING_MODEL = "text-embedding-3-small"
    
    # Timeout Configuration (optimized for medical AI)
    TIMEOUTS = {
        "connection_check": 3600,      # 1 hour
        "model_test": 1800,            # 30 minutes
        "analysis": 3600,              # 1 hour
        "simple_query": 900,           # 15 minutes
        "image_processing": 300,       # 5 minutes
        "multimodal_analysis": 2400    # 40 minutes
    }
    
    # Memory Configuration
    MEMORY_DB_PATH = "agno_teams/dermatology_memory.db"
    MEMORY_TABLE_NAME = "dermatology_memory"
    
    # Knowledge Base Configuration
    KNOWLEDGE_DB_PATH = "agno_teams/tmp/medical_lancedb"
    KNOWLEDGE_TABLE_NAME = "medical_docs"
    
    # Medical Literature URLs for Knowledge Base
    MEDICAL_KNOWLEDGE_URLS = [
        "https://www.aad.org/public/diseases/skin-cancer/types/common/melanoma",
        "https://www.who.int/news-room/fact-sheets/detail/ultraviolet-radiation",
        "https://www.cancer.org/cancer/melanoma-skin-cancer/detection-diagnosis-staging/signs-and-symptoms.html",
        "https://www.mayoclinic.org/diseases-conditions/melanoma/symptoms-causes/syc-20374884"
    ]
    
    # Agent Configuration
    AGENT_CONFIG = {
        "abcde_specialist": {
            "name": "ABCDE Specialist",
            "role": "Expert in ABCDE melanoma analysis",
            "specialization": "asymmetry, border, color, diameter, evolution",
            "timeout_multiplier": 1.0
        },
        "multi_condition_specialist": {
            "name": "Multi-Condition Specialist", 
            "role": "Expert in multi-condition dermatological analysis",
            "specialization": "multiple skin conditions, differential diagnosis",
            "timeout_multiplier": 1.5
        },
        "medical_validator": {
            "name": "Medical Validator",
            "role": "Expert in medical validation and clinical guidelines",
            "specialization": "clinical guidelines, medical standards",
            "timeout_multiplier": 0.8
        },
        "clinical_synthesizer": {
            "name": "Clinical Synthesizer",
            "role": "Expert in synthesizing clinical findings",
            "specialization": "clinical integration, coherent assessment",
            "timeout_multiplier": 1.2
        },
        "clinical_advisor": {
            "name": "Clinical Advisor",
            "role": "Expert in clinical recommendations and patient care",
            "specialization": "recommendations, referrals, patient care",
            "timeout_multiplier": 1.0
        }
    }
    
    # Team Configuration
    TEAM_CONFIG = {
        "main_team": {
            "name": "DermatoGemma Expert Team",
            "mode": "coordinate",
            "description": "Elite team of dermatology specialists",
            "enable_memory": True,
            "enable_knowledge": True,
            "show_responses": True,
            "debug_mode": False
        },
        "high_risk_team": {
            "name": "High-Risk Assessment Team",
            "mode": "collaborate",
            "description": "Specialized team for high-risk lesion assessment",
            "enable_memory": True,
            "show_responses": True,
            "debug_mode": True
        },
        "differential_team": {
            "name": "Differential Diagnosis Team",
            "mode": "collaborate", 
            "description": "Specialized team for complex differential diagnoses",
            "enable_memory": True,
            "show_responses": True,
            "debug_mode": False
        }
    }
    
    # Analysis Configuration
    ANALYSIS_CONFIG = {
        "image_formats": [".jpg", ".jpeg", ".png", ".bmp", ".tiff"],
        "max_image_size": 10 * 1024 * 1024,  # 10MB
        "min_confidence_threshold": 0.7,
        "high_risk_threshold": 0.8,
        "urgent_referral_threshold": 0.9
    }
    
    # Clinical Guidelines
    CLINICAL_GUIDELINES = {
        "abcde_criteria": {
            "asymmetry": "One half does not match the other half",
            "border": "Edges are ragged, notched, or blurred",
            "color": "Color is not uniform throughout",
            "diameter": "Diameter is larger than 6mm (pencil eraser)",
            "evolution": "Mole has changed in size, shape, or color"
        },
        "risk_levels": {
            "low": "Routine follow-up in 6-12 months",
            "medium": "Follow-up in 3-6 months or dermatologist referral",
            "high": "Urgent dermatologist referral within 2 weeks",
            "critical": "Immediate dermatologist referral within 48 hours"
        },
        "referral_criteria": [
            "ABCDE criteria positive",
            "Rapid growth or change",
            "Bleeding or ulceration",
            "Irregular pigmentation",
            "Diameter > 6mm",
            "Patient concern or anxiety"
        ]
    }
    
    # Logging Configuration
    LOGGING_CONFIG = {
        "level": "INFO",
        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        "file": "agno_teams/logs/dermatology_multiagent.log",
        "max_bytes": 10 * 1024 * 1024,  # 10MB
        "backup_count": 5
    }
    
    @classmethod
    def get_model_config(cls) -> Dict[str, Any]:
        """Get model configuration based on environment"""
        if cls.USE_OPENAI and cls.OPENAI_API_KEY:
            return {
                "provider": "openai",
                "chat_model": cls.OPENAI_CHAT_MODEL,
                "embedding_model": cls.OPENAI_EMBEDDING_MODEL,
                "api_key": cls.OPENAI_API_KEY
            }
        else:
            return {
                "provider": "ollama",
                "host": cls.OLLAMA_HOST,
                "model": cls.OLLAMA_MODEL
            }
    
    @classmethod
    def get_paths(cls) -> Dict[str, Path]:
        """Get all configured paths"""
        base_path = Path(__file__).parent
        
        return {
            "base": base_path,
            "memory_db": base_path / cls.MEMORY_DB_PATH,
            "knowledge_db": base_path / cls.KNOWLEDGE_DB_PATH,
            "logs": base_path / "logs",
            "tmp": base_path / "tmp",
            "test_images": base_path.parent / "test_images"
        }
    
    @classmethod
    def setup_directories(cls):
        """Create necessary directories"""
        paths = cls.get_paths()
        
        for path_name, path in paths.items():
            if path_name in ["memory_db", "knowledge_db"]:
                path.parent.mkdir(parents=True, exist_ok=True)
            else:
                path.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def validate_config(cls) -> List[str]:
        """Validate configuration and return any issues"""
        issues = []
        
        # Check model configuration
        if cls.USE_OPENAI and not cls.OPENAI_API_KEY:
            issues.append("OpenAI API key required when USE_OPENAI=true")
        
        # Check paths
        try:
            cls.setup_directories()
        except Exception as e:
            issues.append(f"Failed to create directories: {e}")
        
        return issues

# Environment-specific configurations
class DevelopmentConfig(AgnoTeamsConfig):
    """Development environment configuration"""
    DEBUG_MODE = True
    LOGGING_CONFIG = {
        **AgnoTeamsConfig.LOGGING_CONFIG,
        "level": "DEBUG"
    }

class ProductionConfig(AgnoTeamsConfig):
    """Production environment configuration"""
    DEBUG_MODE = False
    TEAM_CONFIG = {
        **AgnoTeamsConfig.TEAM_CONFIG,
        "main_team": {
            **AgnoTeamsConfig.TEAM_CONFIG["main_team"],
            "debug_mode": False
        }
    }

class TestingConfig(AgnoTeamsConfig):
    """Testing environment configuration"""
    MEMORY_DB_PATH = "agno_teams/test_memory.db"
    KNOWLEDGE_DB_PATH = "agno_teams/tmp/test_medical_lancedb"
    
# Get configuration based on environment
def get_config() -> AgnoTeamsConfig:
    """Get configuration based on environment variable"""
    env = os.getenv("DERMATOGAMMA_ENV", "development").lower()
    
    if env == "production":
        return ProductionConfig()
    elif env == "testing":
        return TestingConfig()
    else:
        return DevelopmentConfig()
