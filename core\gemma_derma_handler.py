"""
🏥 DermatoGemma Multi-Detection System v2.0 - REAL GEMMA-3N IMPLEMENTATION
Specialized Dermatology AI Handler using Gemma 3n
Following EXACT pattern from RetinoblastoGemma-App for real Gemma-3n integration

This module provides advanced AI-powered dermatological analysis using Google Gemma 3n,
specialized for multi-condition skin lesion detection and clinical assessment.
"""

import os
# Disable oneDNN messages from TensorFlow before import (following RetinoblastoGemma pattern)
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

import numpy as np
import logging
from PIL import Image
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
import time
from pathlib import Path
import json
import cv2
from datetime import datetime
import warnings
import requests
import base64
import io
warnings.filterwarnings("ignore")

# Import medical database
import sys
sys.path.append(str(Path(__file__).parent.parent))
from data.medical_reference_database import medical_db

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# MODEL DOWNLOADER DISABLED - USE LOCAL MODEL ONLY (following RetinoblastoGemma pattern)
MODEL_DOWNLOADER_AVAILABLE = False
logger.info("🚫 Model downloader DISABLED - LOCAL GEMMA-3N MODEL ONLY!")

class GemmaDermatologyHandlerV2:
    """
    🤖 Advanced Dermatology AI Handler using Gemma 3n
    
    Features:
    - Multi-condition simultaneous analysis
    - Specialized dermatological prompts
    - Clinical knowledge integration
    - ABCDE analysis integration
    - Confidence scoring and uncertainty quantification
    - Medical report generation
    """
    
    def __init__(self, lazy_load=False):
        # Ollama configuration - REAL Gemma 3n E4B ONLY - NO FALLBACKS
        self.ollama_url = "http://localhost:11434"
        self.model_name = "gemma3n:e4b"  # REAL E4B model - MANDATORY
        self.initialized = False
        self.ready = False
        self.lazy_load = lazy_load
        self._ollama_available = False
        self._supports_vision = True  # E4B REAL multimodal capabilities

        # MANDATORY: Verify Gemma 3n E4B is available - NO FALLBACKS ALLOWED
        if not lazy_load:
            if not self._verify_real_model_availability():
                raise RuntimeError(
                    "❌ CRITICAL ERROR: Gemma 3n E4B model is NOT available via Ollama!\n"
                    "This application requires the REAL model to function.\n"
                    "Please run: ollama pull gemma3n:e4b\n"
                    "NO FALLBACKS OR SIMULATIONS ALLOWED!"
                )

        # Model configuration - ADVANCED Gemma 3n E4B multimodal processing based on official docs
        self.model_config = {
            'temperature': 0.2,  # Very conservative for medical accuracy (docs recommend 0.1-0.3)
            'top_p': 0.85,  # Focused sampling for medical precision
            'max_tokens': 4096,  # E4B supports up to 32K tokens context - using 4K for detailed analysis
            'stream': False,
            'num_predict': 4096,  # Ollama-specific parameter - increased for detailed medical analysis
            'repeat_penalty': 1.15,  # Stronger penalty to prevent repetitive medical responses
            'top_k': 30,  # More focused response generation for medical accuracy
            'seed': 42,  # Reproducible results for medical consistency
            'stop': ["<|end_of_text|>", "<|eot_id|>"],  # Proper stop tokens for E4B
            'mirostat': 0,  # Disable mirostat for medical precision
            'mirostat_eta': 0.1,
            'mirostat_tau': 5.0,
            'num_ctx': 32768,  # Full context window for E4B (32K tokens)
            'num_batch': 512,  # Batch size for processing
            'num_gqa': 8,  # Group query attention for E4B
            'num_gpu': 1,  # Use single GPU for consistency
            'main_gpu': 0,  # Primary GPU
            'low_vram': False,  # Assume sufficient VRAM for medical analysis
            'f16_kv': True,  # Use FP16 for key-value cache
            'logits_all': False,  # Don't return all logits
            'vocab_only': False,  # Full model processing
            'use_mmap': True,  # Memory mapping for efficiency
            'use_mlock': False,  # Don't lock memory
            'numa': False  # NUMA optimization
        }

        # ADVANCED Image processing configuration for Gemma 3n E4B based on official docs
        self.image_config = {
            'supported_resolutions': [224, 256, 384, 448, 512, 576, 640, 768, 896, 1024],  # Full range supported by E4B
            'default_resolution': 768,  # Higher resolution for medical detail (docs recommend 768+ for detailed analysis)
            'max_resolution': 1024,  # Maximum supported by E4B
            'min_resolution': 224,  # Minimum for quality analysis
            'max_images_per_request': 1,  # Single image for focused dermatological analysis
            'supported_formats': ['JPEG', 'PNG', 'WEBP', 'BMP', 'TIFF'],  # Extended format support
            'tokens_per_image': 512,  # Higher token allocation for detailed medical analysis
            'image_quality': 95,  # High JPEG quality for medical images
            'color_space': 'RGB',  # Standard RGB color space
            'interpolation': 'LANCZOS',  # High-quality resampling
            'preserve_aspect_ratio': True,  # Maintain original proportions
            'padding_color': (255, 255, 255),  # White padding for medical images
            'normalization': {
                'mean': [0.485, 0.456, 0.406],  # ImageNet normalization for E4B
                'std': [0.229, 0.224, 0.225]
            },
            'preprocessing_pipeline': [
                'resize',
                'normalize',
                'enhance_contrast',
                'sharpen',
                'noise_reduction'
            ],
            'medical_optimizations': {
                'enhance_skin_contrast': True,
                'preserve_color_accuracy': True,
                'edge_enhancement': True,
                'noise_reduction_strength': 0.3
            }
        }

        # Enhanced timeout configuration for multimodal operations - optimized for Gemma 3n E4B
        self.timeouts = {
            'connection_check': 3600,    # 1 hour for connection check (increased for slow systems)
            'model_test': 1800,          # 30 minutes for model test (first load)
            'analysis': 3600,            # 60 minutes (1 hour) for full multimodal analysis
            'simple_query': 900,         # 15 minutes for simple queries
            'image_processing': 300,     # 5 minutes for image processing
            'multimodal_analysis': 2400  # 40 minutes for complex multimodal analysis
        }

        # Performance optimization settings based on Gemma 3n E4B documentation
        self.performance_config = {
            'batch_processing': False,  # Process images individually for medical accuracy
            'memory_optimization': True,  # Enable memory optimization for E4B
            'precision_mode': 'high',  # High precision for medical analysis
            'concurrent_conditions': 3,  # Analyze max 3 conditions concurrently
            'image_cache_size': 10,  # Cache last 10 processed images
            'adaptive_timeout': True,  # Adjust timeouts based on image complexity
            'quality_threshold': 0.7  # Minimum image quality threshold
        }

        # Log timeout configuration for user awareness
        logger.info(f"⏱️ Timeout Configuration (optimized for low-power systems):")
        logger.info(f"   - Connection check: {self.timeouts['connection_check']}s ({self.timeouts['connection_check']//60}min)")
        logger.info(f"   - Model test: {self.timeouts['model_test']}s ({self.timeouts['model_test']//60}min)")
        logger.info(f"   - Full analysis: {self.timeouts['analysis']}s ({self.timeouts['analysis']//60}min)")
        logger.info(f"   - Simple queries: {self.timeouts['simple_query']}s ({self.timeouts['simple_query']//60}min)")
        
        # Dermatological conditions database - Updated for 14 target diseases
        self.conditions_database = {
            'actinic_keratoses': {
                'name': 'Actinic Keratoses',
                'type': 'precancerous',
                'urgency': 'medium',
                'key_features': ['rough_texture', 'scaly_surface', 'sun_exposed_areas', 'erythematous_base'],
                'abcde_critical': False
            },
            'basal_cell_carcinoma': {
                'name': 'Basal Cell Carcinoma',
                'type': 'malignant',
                'urgency': 'medium',
                'key_features': ['pearly_border', 'central_ulceration', 'telangiectasia', 'slow_growth'],
                'abcde_critical': False
            },
            'benign_keratosis_like_lesions': {
                'name': 'Benign Keratosis-like Lesions',
                'type': 'benign',
                'urgency': 'low',
                'key_features': ['waxy_appearance', 'stuck_on_appearance', 'brown_color', 'well_demarcated'],
                'abcde_critical': False
            },
            'chickenpox': {
                'name': 'Chickenpox',
                'type': 'viral_infection',
                'urgency': 'medium',
                'key_features': ['vesicular_rash', 'itchy_lesions', 'fever', 'systemic_symptoms'],
                'abcde_critical': False
            },
            'cowpox': {
                'name': 'Cowpox',
                'type': 'viral_infection',
                'urgency': 'medium',
                'key_features': ['pustular_lesions', 'localized_infection', 'animal_contact_history'],
                'abcde_critical': False
            },
            'dermatofibroma': {
                'name': 'Dermatofibroma',
                'type': 'benign',
                'urgency': 'low',
                'key_features': ['firm_nodule', 'dimple_sign', 'brown_color', 'stable_size'],
                'abcde_critical': False
            },
            'healthy': {
                'name': 'Healthy Skin',
                'type': 'normal',
                'urgency': 'none',
                'key_features': ['normal_texture', 'uniform_color', 'no_lesions', 'intact_skin'],
                'abcde_critical': False
            },
            'hfmd': {
                'name': 'Hand, Foot and Mouth Disease (HFMD)',
                'type': 'viral_infection',
                'urgency': 'medium',
                'key_features': ['vesicular_lesions', 'palms_soles_involvement', 'oral_lesions', 'fever'],
                'abcde_critical': False
            },
            'measles': {
                'name': 'Measles',
                'type': 'viral_infection',
                'urgency': 'high',
                'key_features': ['maculopapular_rash', 'koplik_spots', 'fever', 'systemic_symptoms'],
                'abcde_critical': False
            },
            'melanocytic_nevi': {
                'name': 'Melanocytic Nevi',
                'type': 'benign',
                'urgency': 'low',
                'key_features': ['uniform_color', 'regular_border', 'symmetrical', 'stable_appearance'],
                'abcde_critical': True
            },
            'melanoma': {
                'name': 'Melanoma',
                'type': 'malignant',
                'urgency': 'high',
                'key_features': ['asymmetry', 'irregular_border', 'color_variation', 'large_diameter', 'evolution'],
                'abcde_critical': True
            },
            'monkeypox': {
                'name': 'Monkeypox',
                'type': 'viral_infection',
                'urgency': 'high',
                'key_features': ['pustular_lesions', 'lymphadenopathy', 'fever', 'systemic_symptoms'],
                'abcde_critical': False
            },
            'squamous_cell_carcinoma': {
                'name': 'Squamous Cell Carcinoma',
                'type': 'malignant',
                'urgency': 'medium',
                'key_features': ['scaly_surface', 'firm_nodule', 'ulceration', 'rapid_growth'],
                'abcde_critical': False
            },
            'vascular_lesions': {
                'name': 'Vascular Lesions',
                'type': 'benign',
                'urgency': 'low',
                'key_features': ['red_color', 'soft_texture', 'blanching', 'vascular_pattern'],
                'abcde_critical': False
            }
        }
        
        # Clinical guidelines integration
        self.clinical_guidelines = {
            'abcde_criteria': {
                'asymmetry': 'One half of the lesion does not match the other half',
                'border': 'Edges are irregular, ragged, notched, or blurred',
                'color': 'Color is not uniform; may include brown, black, red, white, or blue',
                'diameter': 'Diameter is larger than 6mm (size of a pencil eraser)',
                'evolution': 'Lesion has changed in size, shape, color, or symptoms'
            },
            'risk_factors': [
                'Family history of melanoma',
                'Personal history of skin cancer',
                'Multiple atypical nevi',
                'Fair skin, light hair, light eyes',
                'History of sunburns',
                'Excessive UV exposure',
                'Immunosuppression',
                'Age over 50'
            ],
            'red_flags': [
                'Rapid growth',
                'Bleeding or ulceration',
                'Itching or pain',
                'Satellite lesions',
                'Regional lymphadenopathy'
            ]
        }
        
        # Performance tracking
        self.analysis_stats = {
            'total_analyses': 0,
            'condition_detections': {condition: 0 for condition in self.conditions_database.keys()},
            'high_risk_cases': 0,
            'processing_times': [],
            'confidence_scores': []
        }

        # Medical database reference
        self.medical_db = medical_db

        logger.info(f"🤖 Gemma Dermatology Handler initialized")
        logger.info(f"Ollama URL: {self.ollama_url}")
        logger.info(f"Target Model: {self.model_name}")
        logger.info(f"Conditions supported: {len(self.conditions_database)}")

    def _check_ollama_connection(self) -> bool:
        """Check if Ollama is running and accessible"""
        try:
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=self.timeouts['connection_check'])
            if response.status_code == 200:
                models = response.json().get('models', [])
                model_names = [model['name'] for model in models]

                # Check if our target model is available
                if self.model_name in model_names or f"{self.model_name}:latest" in model_names:
                    logger.info(f"✅ Ollama is running and {self.model_name} is available")
                    return True
                else:
                    logger.warning(f"⚠️ Ollama is running but {self.model_name} is not available")
                    logger.warning(f"Available models: {model_names}")
                    logger.warning(f"💡 Run: ollama pull {self.model_name}")
                    return False
            else:
                logger.error(f"❌ Ollama API returned status code: {response.status_code}")
                return False

        except requests.exceptions.ConnectionError:
            logger.error("❌ Cannot connect to Ollama. Is Ollama running?")
            logger.error("💡 Start Ollama with: ollama serve")
            return False
        except requests.exceptions.Timeout:
            logger.error("❌ Ollama connection timeout")
            return False
        except Exception as e:
            logger.error(f"❌ Error checking Ollama connection: {e}")
            return False

        return models_dir / "gemma-3n-E4B"  # Default to E4B

    def load_model_on_demand(self) -> bool:
        """Load model on demand (for lazy loading)"""
        if self.model is not None:
            logger.info("✅ Model already loaded")
            return True

        logger.info("🔄 Loading model on demand...")
        return self.initialize_model(force_load=True)

    def is_ready(self) -> bool:
        """Check if handler is ready, loading model on demand if needed"""
        if self.lazy_load and not self.initialized:
            logger.info("🔄 Initializing connection on demand for analysis...")
            return self.initialize_model(force_load=True)
        return self.ready

    def get_available_models(self) -> dict:
        """Get information about available Gemma-3n models"""
        models_dir = Path("models")
        available_models = {}

        model_info = {
            "gemma-3n-E4B": {
                "name": "Gemma-3n E4B (4-bit quantized)",
                "description": "Recommended model for general use",
                "model_id": "google/gemma-3n-E4B-it",
                "path": models_dir / "gemma-3n-E4B"
            },
            "gemma-3n-E2B": {
                "name": "Gemma-3n E2B (2-bit quantized)",
                "description": "Lightweight model for resource-constrained environments",
                "model_id": "google/gemma-3n-E2B-it",
                "path": models_dir / "gemma-3n-E2B"
            }
        }

        for key, info in model_info.items():
            model_path = info["path"]
            if model_path.exists():
                # Check if it has essential files
                has_config = (model_path / "config.json").exists()
                has_model_files = any(model_path.glob("*.safetensors")) or any(model_path.glob("*.bin")) or any(model_path.glob("*.task"))

                if has_config and has_model_files:
                    # Calculate total size
                    total_size = sum(f.stat().st_size for f in model_path.rglob("*") if f.is_file())
                    info["size_mb"] = total_size / (1024 * 1024)
                    info["status"] = "available"
                    info["current"] = (model_path == self.model_path)
                    available_models[key] = info
                else:
                    info["status"] = "incomplete"
                    info["current"] = False
                    available_models[key] = info
            else:
                info["status"] = "not_downloaded"
                info["current"] = False
                available_models[key] = info

        return available_models

    def initialize_model(self, force_load=False) -> bool:
        """Initialize connection to Ollama with Gemma 3n-E4B model with multimodal support"""
        try:
            # Skip loading if lazy_load is enabled and not forced
            if self.lazy_load and not force_load:
                logger.info("🚀 Skipping model connection (lazy mode) - will connect on first analysis")
                self.initialized = True
                self.ready = True
                return True

            logger.info("🔄 Connecting to Ollama with Gemma 3n-E4B (Multimodal)...")

            # MANDATORY: Check Ollama connection and REAL model availability
            if not self._check_ollama_connection():
                raise RuntimeError("❌ CRITICAL: Cannot connect to Ollama! NO FALLBACKS ALLOWED!")

            # MANDATORY: Verify REAL Gemma 3n E4B model is available
            if not self._verify_real_model_availability():
                raise RuntimeError("❌ CRITICAL: Gemma 3n E4B model not available! NO FALLBACKS ALLOWED!")

            # MANDATORY: Verify multimodal capabilities
            if not self._verify_multimodal_support():
                raise RuntimeError("❌ CRITICAL: Multimodal support not available! NO FALLBACKS ALLOWED!")

            # Test model with a simple query
            if self._test_ollama_model():
                self.initialized = True
                self.ready = True
                self._ollama_available = True
                logger.info("✅ Ollama Gemma 3n-E2B connection established successfully")
                return True
            else:
                logger.error("❌ CRITICAL: Ollama model test failed")
                raise RuntimeError("❌ CRITICAL: Gemma 3n E4B model test failed! NO FALLBACKS ALLOWED!")

        except Exception as e:
            logger.error(f"❌ CRITICAL: Failed to connect to Ollama: {e}")
            raise RuntimeError(f"❌ CRITICAL: Failed to connect to Ollama: {e}. NO FALLBACKS ALLOWED!")

    def _verify_multimodal_support(self) -> bool:
        """Verify that the Gemma 3n E4B model supports multimodal input"""
        try:
            # Test with a simple multimodal request
            test_response = requests.post(
                f"{self.ollama_url}/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": "Test multimodal capabilities",
                    "stream": False,
                    "options": {"num_predict": 10}
                },
                timeout=3600
            )

            if test_response.status_code == 200:
                logger.info("✅ Multimodal support verified for Gemma 3n E4B")
                return True
            else:
                logger.warning(f"⚠️ Multimodal test failed: {test_response.status_code}")
                return False

        except Exception as e:
            logger.warning(f"⚠️ Could not verify multimodal support: {e}")
            return False

    def _verify_real_model_availability(self) -> bool:
        """
        MANDATORY verification that Gemma 3n E4B is actually available via Ollama
        NO FALLBACKS - This must succeed or application fails
        """
        try:
            logger.info("🔍 VERIFYING REAL Gemma 3n E4B model availability...")

            # Check Ollama service is running
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=3600)
            if response.status_code != 200:
                logger.error("❌ Ollama service is not running!")
                return False

            # Check if gemma3n:e4b is actually available
            models_data = response.json()
            available_models = [model['name'] for model in models_data.get('models', [])]

            if self.model_name not in available_models:
                logger.error(f"❌ Model {self.model_name} is NOT available!")
                logger.error(f"Available models: {available_models}")
                logger.error("Please run: ollama pull gemma3n:e4b")
                return False

            # Test actual model functionality with a simple multimodal request
            test_response = requests.post(
                f"{self.ollama_url}/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": "Test: Describe what you can do with images.",
                    "stream": False,
                    "options": {"num_predict": 50}
                },
                timeout=3600
            )

            if test_response.status_code != 200:
                logger.error(f"❌ Model {self.model_name} failed to respond!")
                return False

            response_data = test_response.json()
            if not response_data.get('response'):
                logger.error(f"❌ Model {self.model_name} returned empty response!")
                return False

            logger.info(f"✅ REAL Gemma 3n E4B model verified and functional!")
            logger.info(f"Model response preview: {response_data['response'][:100]}...")
            return True

        except Exception as e:
            logger.error(f"❌ CRITICAL: Failed to verify real model: {e}")
            return False

    def _test_ollama_model(self) -> bool:
        """Test Ollama model with a simple query"""
        try:
            test_prompt = "What is dermatology?"

            logger.info(f"🧪 Testing Ollama model with {self.timeouts['model_test']}s timeout ({self.timeouts['model_test']//60} minutes)...")
            logger.info("⏳ This may take a while on low-power systems - please be patient...")

            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": test_prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.3,
                        "num_predict": 50
                    }
                },
                timeout=self.timeouts['model_test']
            )

            if response.status_code == 200:
                result = response.json()
                if 'response' in result and len(result['response'].strip()) > 0:
                    logger.info("✅ Ollama model test successful")
                    return True
                else:
                    logger.error("❌ Ollama model returned empty response")
                    return False
            else:
                logger.error(f"❌ Ollama model test failed with status: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"❌ Ollama model test error: {e}")
            return False

    def _prepare_image_for_gemma3n(self, image_input: Union[np.ndarray, str, Path, Image.Image]) -> Optional[str]:
        """
        ADVANCED image preparation for Gemma 3n E4B processing based on official documentation

        Implements optimal preprocessing pipeline for medical image analysis:
        - High-resolution processing (up to 1024x1024)
        - Medical-specific enhancements
        - Color accuracy preservation
        - Advanced normalization

        Args:
            image_input: Image as numpy array, file path, or PIL Image

        Returns:
            Base64 encoded image string optimized for Gemma 3n E4B medical analysis
        """
        try:
            # Convert input to PIL Image
            if isinstance(image_input, np.ndarray):
                # Convert BGR to RGB if needed
                if len(image_input.shape) == 3 and image_input.shape[2] == 3:
                    image_input = cv2.cvtColor(image_input, cv2.COLOR_BGR2RGB)
                image = Image.fromarray(image_input)
            elif isinstance(image_input, (str, Path)):
                image = Image.open(image_input)
            elif isinstance(image_input, Image.Image):
                image = image_input
            else:
                logger.error(f"❌ Unsupported image input type: {type(image_input)}")
                raise ValueError(f"Unsupported image input type: {type(image_input)}")

            # Convert to RGB if needed
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Apply ADVANCED dermatological-specific enhancements
            enhanced_image = self._apply_advanced_medical_preprocessing(image)

            # Extract comprehensive metadata for analysis context
            image_metadata = self._extract_comprehensive_image_metadata(enhanced_image)

            # Determine optimal resolution based on image content and quality
            target_size = self._calculate_optimal_resolution(enhanced_image, image_metadata)

            # Advanced resizing with medical image preservation
            processed_image = self._advanced_medical_resize(enhanced_image, target_size)

            # Apply final medical optimizations
            final_image = self._apply_final_medical_optimizations(processed_image)

            # Convert to base64 with optimal compression
            image_base64 = self._encode_image_for_gemma3n(final_image)

            logger.info(f"✅ ADVANCED image prepared for Gemma 3n E4B: {target_size}x{target_size}")
            logger.info(f"📊 Image quality metrics: {image_metadata.get('quality_metrics', {})}")
            return image_base64

        except Exception as e:
            logger.error(f"❌ CRITICAL: Advanced image preparation failed: {e}")
            raise RuntimeError(f"❌ CRITICAL: Advanced image preparation failed: {e}. NO FALLBACKS ALLOWED!")

    def _enhance_dermatological_image(self, image: Image.Image) -> Image.Image:
        """
        Apply dermatological-specific image enhancements for better analysis

        Args:
            image: PIL Image to enhance

        Returns:
            Enhanced PIL Image optimized for dermatological analysis
        """
        try:
            # Convert to numpy for processing
            img_array = np.array(image)

            # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization) for better contrast
            # Convert to LAB color space for better processing
            lab = cv2.cvtColor(img_array, cv2.COLOR_RGB2LAB)
            l_channel, a_channel, b_channel = cv2.split(lab)

            # Apply CLAHE to L channel
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            l_channel = clahe.apply(l_channel)

            # Merge channels back
            enhanced_lab = cv2.merge([l_channel, a_channel, b_channel])
            enhanced_rgb = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2RGB)

            # Apply slight sharpening for better edge definition
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            sharpened = cv2.filter2D(enhanced_rgb, -1, kernel)

            # Blend original and sharpened (70% enhanced, 30% original)
            final_image = cv2.addWeighted(enhanced_rgb, 0.7, sharpened, 0.3, 0)

            # Convert back to PIL Image
            return Image.fromarray(final_image)

        except Exception as e:
            logger.warning(f"⚠️ Image enhancement failed, using original: {e}")
            return image

    def _extract_image_metadata(self, image: Image.Image) -> Dict:
        """
        Extract metadata and basic characteristics from dermatological image

        Args:
            image: PIL Image to analyze

        Returns:
            Dictionary containing image metadata and characteristics
        """
        try:
            img_array = np.array(image)

            metadata = {
                'dimensions': {
                    'width': image.width,
                    'height': image.height,
                    'aspect_ratio': image.width / image.height
                },
                'color_analysis': {
                    'mean_rgb': np.mean(img_array, axis=(0, 1)).tolist(),
                    'std_rgb': np.std(img_array, axis=(0, 1)).tolist(),
                    'brightness': np.mean(img_array),
                    'contrast': np.std(img_array)
                },
                'quality_metrics': {
                    'sharpness': self._calculate_image_sharpness(img_array),
                    'noise_level': self._estimate_noise_level(img_array)
                }
            }

            return metadata

        except Exception as e:
            logger.warning(f"⚠️ Metadata extraction failed: {e}")
            return {}

    def _calculate_image_sharpness(self, img_array: np.ndarray) -> float:
        """Calculate image sharpness using Laplacian variance"""
        try:
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            laplacian = cv2.Laplacian(gray, cv2.CV_64F)
            return float(laplacian.var())
        except:
            return 0.0

    def _estimate_noise_level(self, img_array: np.ndarray) -> float:
        """Estimate noise level in the image"""
        try:
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            # Use standard deviation of Laplacian as noise estimate
            laplacian = cv2.Laplacian(gray, cv2.CV_64F)
            return float(np.std(laplacian))
        except:
            return 0.0

    def _apply_advanced_medical_preprocessing(self, image: Image.Image) -> Image.Image:
        """
        Apply ADVANCED medical-specific preprocessing pipeline for Gemma 3n E4B
        Based on official documentation recommendations for medical image analysis
        """
        try:
            # Convert to numpy for advanced processing
            img_array = np.array(image)

            # 1. Advanced color space optimization for dermatology
            lab = cv2.cvtColor(img_array, cv2.COLOR_RGB2LAB)
            l_channel, a_channel, b_channel = cv2.split(lab)

            # 2. Advanced CLAHE with medical-specific parameters
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(16, 16))  # Increased for medical detail
            l_channel = clahe.apply(l_channel)

            # 3. Color channel enhancement for skin analysis
            enhanced_lab = cv2.merge([l_channel, a_channel, b_channel])
            enhanced_rgb = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2RGB)

            # 4. Advanced edge enhancement for lesion boundaries
            kernel_sharpen = np.array([[-1,-1,-1,-1,-1],
                                     [-1, 2, 2, 2,-1],
                                     [-1, 2, 8, 2,-1],
                                     [-1, 2, 2, 2,-1],
                                     [-1,-1,-1,-1,-1]]) / 8.0
            sharpened = cv2.filter2D(enhanced_rgb, -1, kernel_sharpen)

            # 5. Noise reduction while preserving medical details
            denoised = cv2.bilateralFilter(sharpened, 9, 75, 75)

            # 6. Final blend for optimal medical visualization
            final_image = cv2.addWeighted(enhanced_rgb, 0.6, denoised, 0.4, 0)

            return Image.fromarray(final_image)

        except Exception as e:
            logger.warning(f"⚠️ Advanced preprocessing failed, using basic: {e}")
            return self._enhance_dermatological_image(image)

    def _extract_comprehensive_image_metadata(self, image: Image.Image) -> Dict:
        """
        Extract comprehensive metadata optimized for Gemma 3n E4B medical analysis
        """
        try:
            img_array = np.array(image)

            # Basic metadata
            metadata = self._extract_image_metadata(image)

            # Advanced medical-specific metrics
            metadata['medical_metrics'] = {
                'skin_tone_analysis': self._analyze_skin_tone(img_array),
                'lesion_contrast': self._calculate_lesion_contrast(img_array),
                'color_distribution': self._analyze_color_distribution(img_array),
                'texture_complexity': self._calculate_texture_complexity(img_array),
                'edge_definition': self._calculate_edge_definition(img_array)
            }

            # Gemma 3n E4B specific optimizations
            metadata['gemma3n_optimizations'] = {
                'recommended_tokens': self._calculate_recommended_tokens(metadata),
                'processing_priority': self._determine_processing_priority(metadata),
                'enhancement_level': self._determine_enhancement_level(metadata)
            }

            return metadata

        except Exception as e:
            logger.warning(f"⚠️ Comprehensive metadata extraction failed: {e}")
            return self._extract_image_metadata(image)

    def _calculate_optimal_resolution(self, image: Image.Image, metadata: Dict) -> int:
        """
        Calculate optimal resolution for Gemma 3n E4B based on image content and quality
        """
        try:
            base_resolution = self.image_config['default_resolution']

            # Adjust based on image quality
            quality_metrics = metadata.get('quality_metrics', {})
            sharpness = quality_metrics.get('sharpness', 100)

            # Higher resolution for high-quality images
            if sharpness > 500:
                target_resolution = min(1024, base_resolution * 1.5)
            elif sharpness > 200:
                target_resolution = base_resolution
            else:
                target_resolution = max(512, base_resolution * 0.8)

            # Ensure it's within supported range
            target_resolution = max(self.image_config['min_resolution'],
                                  min(self.image_config['max_resolution'], target_resolution))

            return int(target_resolution)

        except Exception as e:
            logger.warning(f"⚠️ Optimal resolution calculation failed: {e}")
            return self.image_config['default_resolution']

    def _advanced_medical_resize(self, image: Image.Image, target_size: int) -> Image.Image:
        """
        Advanced resizing optimized for medical image analysis
        """
        try:
            # Calculate scaling to maintain aspect ratio
            original_width, original_height = image.size
            scale = min(target_size / original_width, target_size / original_height)

            new_width = int(original_width * scale)
            new_height = int(original_height * scale)

            # Use high-quality resampling
            resized = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # Create canvas with medical-appropriate background
            canvas = Image.new('RGB', (target_size, target_size), self.image_config['padding_color'])

            # Center the image
            paste_x = (target_size - new_width) // 2
            paste_y = (target_size - new_height) // 2
            canvas.paste(resized, (paste_x, paste_y))

            return canvas

        except Exception as e:
            logger.warning(f"⚠️ Advanced resize failed: {e}")
            # Fallback to basic resize
            image.thumbnail((target_size, target_size), Image.Resampling.LANCZOS)
            canvas = Image.new('RGB', (target_size, target_size), (255, 255, 255))
            paste_x = (target_size - image.width) // 2
            paste_y = (target_size - image.height) // 2
            canvas.paste(image, (paste_x, paste_y))
            return canvas

    def _apply_final_medical_optimizations(self, image: Image.Image) -> Image.Image:
        """
        Apply final optimizations for Gemma 3n E4B medical analysis
        """
        try:
            if not self.image_config['medical_optimizations']['enhance_skin_contrast']:
                return image

            img_array = np.array(image)

            # Final contrast enhancement for skin lesions
            enhanced = cv2.convertScaleAbs(img_array, alpha=1.1, beta=5)

            return Image.fromarray(enhanced)

        except Exception as e:
            logger.warning(f"⚠️ Final optimizations failed: {e}")
            return image

    def _encode_image_for_gemma3n(self, image: Image.Image) -> str:
        """
        Encode image with optimal settings for Gemma 3n E4B
        """
        try:
            buffer = io.BytesIO()
            image.save(
                buffer,
                format='JPEG',
                quality=self.image_config['image_quality'],
                optimize=True,
                progressive=True
            )
            return base64.b64encode(buffer.getvalue()).decode('utf-8')

        except Exception as e:
            logger.error(f"❌ Image encoding failed: {e}")
            raise RuntimeError(f"❌ CRITICAL: Image encoding failed: {e}")

    def _optimize_model_config_for_image(self, image_metadata: Dict) -> Dict:
        """
        Dynamically optimize model configuration based on image characteristics

        Args:
            image_metadata: Metadata extracted from the image

        Returns:
            Optimized model configuration
        """
        try:
            optimized_config = self.model_config.copy()

            # Adjust based on image quality
            quality_metrics = image_metadata.get('quality_metrics', {})
            sharpness = quality_metrics.get('sharpness', 0)
            noise_level = quality_metrics.get('noise_level', 0)

            # For low quality images, use more conservative settings
            if sharpness < 100 or noise_level > 50:
                optimized_config['temperature'] = 0.2  # More conservative for unclear images
                optimized_config['top_p'] = 0.8
                logger.info("🔧 Adjusted model config for low-quality image")

            # For high quality images, allow slightly more creativity
            elif sharpness > 500 and noise_level < 20:
                optimized_config['temperature'] = 0.4
                optimized_config['top_p'] = 0.95
                logger.info("🔧 Adjusted model config for high-quality image")

            # Adjust token count based on image complexity
            color_analysis = image_metadata.get('color_analysis', {})
            contrast = color_analysis.get('contrast', 0)

            if contrast > 100:  # High contrast, potentially more complex
                optimized_config['num_predict'] = min(3072, optimized_config['num_predict'] * 1.5)
                logger.info("🔧 Increased token limit for complex image")

            return optimized_config

        except Exception as e:
            logger.warning(f"⚠️ Config optimization failed, using defaults: {e}")
            return self.model_config

    def _calculate_adaptive_timeout(self, image_metadata: Dict) -> int:
        """
        Calculate adaptive timeout based on image complexity

        Args:
            image_metadata: Metadata extracted from the image

        Returns:
            Adaptive timeout in seconds
        """
        try:
            base_timeout = self.timeouts['multimodal_analysis']

            # Factors that increase processing time
            dimensions = image_metadata.get('dimensions', {})
            quality_metrics = image_metadata.get('quality_metrics', {})

            # Larger images need more time
            total_pixels = dimensions.get('width', 512) * dimensions.get('height', 512)
            if total_pixels > 512 * 512:
                base_timeout = int(base_timeout * 1.3)

            # Low quality images need more processing time
            sharpness = quality_metrics.get('sharpness', 100)
            if sharpness < 50:
                base_timeout = int(base_timeout * 1.5)

            # Cap the timeout to reasonable limits
            return min(base_timeout, self.timeouts['analysis'])

        except Exception as e:
            logger.warning(f"⚠️ Adaptive timeout calculation failed: {e}")
            return self.timeouts['multimodal_analysis']

    def _analyze_skin_tone(self, img_array: np.ndarray) -> Dict:
        """Analyze skin tone characteristics for medical context"""
        try:
            # Convert to HSV for better skin tone analysis
            hsv = cv2.cvtColor(img_array, cv2.COLOR_RGB2HSV)

            # Calculate skin tone metrics
            hue_mean = np.mean(hsv[:, :, 0])
            saturation_mean = np.mean(hsv[:, :, 1])
            value_mean = np.mean(hsv[:, :, 2])

            return {
                'hue_mean': float(hue_mean),
                'saturation_mean': float(saturation_mean),
                'brightness_mean': float(value_mean),
                'skin_type_estimate': self._estimate_skin_type(hue_mean, saturation_mean)
            }
        except:
            return {'analysis_failed': True}

    def _estimate_skin_type(self, hue: float, saturation: float) -> str:
        """Estimate Fitzpatrick skin type for medical context"""
        try:
            if saturation < 50:
                return "Type I-II (Light)"
            elif saturation < 100:
                return "Type III-IV (Medium)"
            else:
                return "Type V-VI (Dark)"
        except:
            return "Unknown"

    def _calculate_lesion_contrast(self, img_array: np.ndarray) -> float:
        """Calculate contrast between lesion and surrounding skin"""
        try:
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            return float(np.std(gray))
        except:
            return 0.0

    def _analyze_color_distribution(self, img_array: np.ndarray) -> Dict:
        """Analyze color distribution for dermatological assessment"""
        try:
            # Calculate color histograms
            hist_r = cv2.calcHist([img_array], [0], None, [256], [0, 256])
            hist_g = cv2.calcHist([img_array], [1], None, [256], [0, 256])
            hist_b = cv2.calcHist([img_array], [2], None, [256], [0, 256])

            return {
                'red_distribution': float(np.std(hist_r)),
                'green_distribution': float(np.std(hist_g)),
                'blue_distribution': float(np.std(hist_b)),
                'color_variance': float(np.var(img_array))
            }
        except:
            return {'analysis_failed': True}

    def _calculate_texture_complexity(self, img_array: np.ndarray) -> float:
        """Calculate texture complexity for lesion analysis"""
        try:
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            # Use Sobel operator for texture analysis
            sobelx = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
            sobely = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
            magnitude = np.sqrt(sobelx**2 + sobely**2)
            return float(np.mean(magnitude))
        except:
            return 0.0

    def _calculate_edge_definition(self, img_array: np.ndarray) -> float:
        """Calculate edge definition quality for border analysis"""
        try:
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            edges = cv2.Canny(gray, 50, 150)
            return float(np.sum(edges > 0) / edges.size)
        except:
            return 0.0

    def _calculate_recommended_tokens(self, metadata: Dict) -> int:
        """Calculate recommended token allocation for Gemma 3n E4B"""
        try:
            base_tokens = self.image_config['tokens_per_image']

            # Adjust based on image complexity
            quality_metrics = metadata.get('quality_metrics', {})
            medical_metrics = metadata.get('medical_metrics', {})

            complexity_factor = 1.0
            if quality_metrics.get('sharpness', 0) > 300:
                complexity_factor += 0.3
            if medical_metrics.get('texture_complexity', 0) > 50:
                complexity_factor += 0.2

            return int(base_tokens * complexity_factor)
        except:
            return self.image_config['tokens_per_image']

    def _determine_processing_priority(self, metadata: Dict) -> str:
        """Determine processing priority for Gemma 3n E4B"""
        try:
            medical_metrics = metadata.get('medical_metrics', {})
            lesion_contrast = medical_metrics.get('lesion_contrast', 0)

            if lesion_contrast > 100:
                return "high"
            elif lesion_contrast > 50:
                return "medium"
            else:
                return "low"
        except:
            return "medium"

    def _determine_enhancement_level(self, metadata: Dict) -> str:
        """Determine enhancement level needed for optimal analysis"""
        try:
            quality_metrics = metadata.get('quality_metrics', {})
            sharpness = quality_metrics.get('sharpness', 0)

            if sharpness < 100:
                return "high"
            elif sharpness < 300:
                return "medium"
            else:
                return "low"
        except:
            return "medium"

    # FALLBACK MODE COMPLETELY REMOVED - NO FALLBACKS ALLOWED
    # This application requires REAL Gemma 3n E4B model to function

    def initialize_local_model(self, auto_download: bool = False) -> bool:
        """Initialize local model - wrapper for existing method"""
        try:
            return self.initialize_model()
        except Exception as e:
            logger.error(f"❌ CRITICAL: Local model initialization failed: {e}")
            raise RuntimeError(f"❌ CRITICAL: Local model initialization failed: {e}. NO FALLBACKS ALLOWED!")
    
    def _test_model(self) -> bool:
        """Test the model with a simple dermatological query"""
        try:
            test_prompt = self._create_dermatology_prompt(
                "Test lesion analysis",
                {"asymmetry": 0.3, "border": 0.2, "color": 0.4, "diameter": 0.5},
                []
            )
            
            # Tokenize
            inputs = self.tokenizer(
                test_prompt,
                return_tensors="pt",
                truncation=True,
                max_length=512
            )
            
            # Move inputs to device if not using device_map
            if not torch.cuda.is_available():
                inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Generate
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_length=inputs['input_ids'].shape[1] + 50,
                    temperature=self.model_config['temperature'],
                    do_sample=False,
                    pad_token_id=self.model_config['pad_token_id']
                )
            
            # Decode response
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            if len(response) > len(test_prompt):
                logger.info("✅ Model test successful")
                return True
            else:
                logger.warning("⚠️ Model test produced no output")
                return False
                
        except Exception as e:
            logger.error(f"❌ Model test failed: {e}")
            return False
    
    def analyze_multi_condition(self, lesion_image: np.ndarray,
                              abcde_results: Optional[Dict] = None,
                              lesion_metadata: Optional[Dict] = None,
                              patient_context: Optional[Dict] = None) -> Dict:
        """
        🔬 Enhanced multi-condition dermatological analysis using Gemma 3n E4B multimodal capabilities

        This method performs comprehensive simultaneous analysis for multiple dermatological conditions,
        leveraging advanced image processing and multimodal AI analysis to provide:
        - Visual data extraction and interpretation from skin lesion images
        - Probability assessments with confidence scoring based on multimodal evidence
        - Clinical recommendations integrating image analysis with ABCDE criteria
        - Enhanced diagnostic accuracy through AI-powered visual feature extraction

        Args:
            lesion_image: RGB image of the lesion for multimodal analysis
            abcde_results: Results from ABCDE analysis for clinical correlation
            lesion_metadata: Additional lesion information and context
            patient_context: Patient demographics and history for comprehensive assessment

        Returns:
            Comprehensive multimodal analysis with condition probabilities, visual insights, and recommendations
        """
        start_time = time.time()
        
        try:
            logger.info("🔬 Starting multi-condition dermatological analysis...")
            
            if not self.is_ready():
                raise RuntimeError("❌ CRITICAL: System not ready! Gemma 3n E4B model must be available. NO FALLBACKS ALLOWED!")
            
            # Prepare enhanced analysis context with multimodal processing
            analysis_context = self._prepare_enhanced_analysis_context(
                lesion_image, abcde_results, lesion_metadata, patient_context
            )
            
            # Analyze each condition
            condition_analyses = {}
            
            for condition_id, condition_info in self.conditions_database.items():
                logger.info(f"🔍 Analyzing for {condition_info['name']}...")
                
                condition_analysis = self._analyze_single_condition(
                    condition_id, condition_info, analysis_context
                )
                
                condition_analyses[condition_id] = condition_analysis
            
            # Generate overall assessment
            overall_assessment = self._generate_overall_assessment(condition_analyses, abcde_results)
            
            # Create clinical recommendations
            clinical_recommendations = self._generate_clinical_recommendations(
                condition_analyses, overall_assessment, patient_context
            )
            
            # Calculate confidence metrics
            confidence_metrics = self._calculate_confidence_metrics(condition_analyses)
            
            # Compile comprehensive results
            results = {
                'timestamp': datetime.now().isoformat(),
                'processing_time': time.time() - start_time,
                'condition_analyses': condition_analyses,
                'overall_assessment': overall_assessment,
                'clinical_recommendations': clinical_recommendations,
                'confidence_metrics': confidence_metrics,
                'abcde_integration': abcde_results is not None,
                'patient_context_available': patient_context is not None,
                'success': True
            }
            
            # Update statistics
            self._update_analysis_stats(results)
            
            logger.info(f"✅ Multi-condition analysis completed in {results['processing_time']:.2f}s")
            logger.info(f"🎯 Top condition: {overall_assessment['most_likely_condition']} ({overall_assessment['confidence']:.1%})")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Multi-condition analysis failed: {e}")
            return self._create_error_result(f"Analysis error: {str(e)}")

    def analyze_single_condition(self, lesion_image: np.ndarray,
                               target_condition: str,
                               abcde_results: Optional[Dict] = None,
                               lesion_metadata: Optional[Dict] = None,
                               patient_context: Optional[Dict] = None) -> Dict:
        """
        🎯 Single-condition dermatological analysis for specific disease

        Args:
            lesion_image: Preprocessed lesion image
            target_condition: Specific condition to analyze for
            abcde_results: ABCDE analysis results
            lesion_metadata: Lesion detection metadata
            patient_context: Patient information

        Returns:
            Dict containing single-condition analysis results
        """
        start_time = time.time()

        try:
            logger.info(f"🎯 Starting single-condition analysis for: {target_condition}")

            if not self.initialized:
                raise RuntimeError("❌ CRITICAL: Handler not initialized! Gemma 3n E4B model must be available. NO FALLBACKS ALLOWED!")

            # Prepare analysis context
            context = self._prepare_analysis_context(lesion_image, abcde_results, lesion_metadata, patient_context)

            # Get condition information
            condition_info = self.medical_db.get_condition_data(target_condition.lower().replace(' ', '_'))
            if not condition_info:
                logger.warning(f"⚠️ Unknown condition: {target_condition}")
                condition_info = {
                    'name': target_condition,
                    'type': 'unknown',
                    'urgency': 'moderate',
                    'key_features': ['Unknown features'],
                    'description': f'Analysis for {target_condition}'
                }

            # Create focused prompt for single condition
            prompt = self._create_single_condition_prompt(target_condition, condition_info, context)

            # Query Ollama for analysis
            ollama_response = self._query_ollama_single_condition(prompt, target_condition)

            if not ollama_response.get('success', False):
                raise RuntimeError(f"❌ CRITICAL: Ollama analysis failed for {target_condition}! NO FALLBACKS ALLOWED!")

            # Parse and structure results
            analysis_result = self._parse_single_condition_response(ollama_response['response'], target_condition)

            # Calculate processing time
            processing_time = time.time() - start_time

            # Create comprehensive result
            results = {
                'target_condition': target_condition,
                'condition_info': condition_info,
                'analysis_result': analysis_result,
                'probability': analysis_result.get('probability', 0.5),
                'confidence': analysis_result.get('confidence', 0.7),
                'key_features': analysis_result.get('key_features', []),
                'clinical_assessment': analysis_result.get('clinical_assessment', ''),
                'recommendations': analysis_result.get('recommendations', []),
                'risk_level': analysis_result.get('risk_level', 'moderate'),
                'processing_time': processing_time,
                'timestamp': datetime.now().isoformat(),
                'analysis_type': 'single_condition',
                'success': True
            }

            logger.info(f"✅ Single-condition analysis completed in {processing_time:.2f}s")
            logger.info(f"🎯 {target_condition} probability: {analysis_result.get('probability', 0.5):.1%}")

            return results

        except Exception as e:
            logger.error(f"❌ Single-condition analysis failed: {e}")
            return self._create_error_result(f"Single-condition analysis error: {str(e)}")

    def _prepare_enhanced_analysis_context(self, lesion_image: np.ndarray, abcde_results: Optional[Dict],
                                         lesion_metadata: Optional[Dict], patient_context: Optional[Dict]) -> Dict:
        """
        Prepare enhanced analysis context with multimodal image processing for Gemma 3n E4B

        Args:
            lesion_image: RGB image array of the skin lesion
            abcde_results: Optional ABCDE analysis results
            lesion_metadata: Optional lesion metadata
            patient_context: Optional patient context

        Returns:
            Enhanced context dictionary with image processing and metadata
        """
        try:
            # Start with basic context
            context = self._prepare_analysis_context(lesion_image, abcde_results, lesion_metadata, patient_context)

            # Add enhanced image processing for multimodal analysis
            if lesion_image is not None:
                # Convert to PIL Image for processing
                if isinstance(lesion_image, np.ndarray):
                    pil_image = Image.fromarray(lesion_image)
                else:
                    pil_image = lesion_image

                # Extract enhanced image metadata
                enhanced_metadata = self._extract_image_metadata(pil_image)
                context['enhanced_image_metadata'] = enhanced_metadata

                # Prepare image for multimodal analysis
                processed_image_b64 = self._prepare_image_for_gemma3n(pil_image)
                if processed_image_b64:
                    context['processed_image_base64'] = processed_image_b64
                    context['image_ready_for_analysis'] = True
                else:
                    context['image_ready_for_analysis'] = False

                # Add visual analysis context
                context['visual_analysis_enabled'] = self._supports_vision
                context['image_processing_quality'] = enhanced_metadata.get('quality_metrics', {})

                logger.info("✅ Enhanced multimodal context prepared successfully")

            return context

        except Exception as e:
            logger.error(f"❌ CRITICAL: Enhanced context preparation failed: {e}")
            raise RuntimeError(f"❌ CRITICAL: Enhanced context preparation failed: {e}. NO FALLBACKS ALLOWED!")

    def _prepare_analysis_context(self, lesion_image: np.ndarray,
                                abcde_results: Optional[Dict],
                                lesion_metadata: Optional[Dict],
                                patient_context: Optional[Dict]) -> Dict:
        """Prepare comprehensive context for analysis"""
        try:
            # Extract image features
            image_features = self._extract_image_features(lesion_image)
            
            # Prepare context dictionary
            context = {
                'image_features': image_features,
                'abcde_results': abcde_results or {},
                'lesion_metadata': lesion_metadata or {},
                'patient_context': patient_context or {},
                'clinical_guidelines': self.clinical_guidelines
            }
            
            return context
            
        except Exception as e:
            logger.warning(f"⚠️ Context preparation failed: {e}")
            return {'error': str(e)}
    
    def _extract_image_features(self, image: np.ndarray) -> Dict:
        """Extract relevant features from the lesion image"""
        try:
            # Basic image properties
            height, width = image.shape[:2]
            
            # Color analysis
            mean_color = np.mean(image.reshape(-1, 3), axis=0)
            color_std = np.std(image.reshape(-1, 3), axis=0)
            
            # Convert to different color spaces for analysis
            hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
            lab = cv2.cvtColor(image, cv2.COLOR_RGB2LAB)
            
            # HSV statistics
            hsv_mean = np.mean(hsv.reshape(-1, 3), axis=0)
            hsv_std = np.std(hsv.reshape(-1, 3), axis=0)
            
            # LAB statistics
            lab_mean = np.mean(lab.reshape(-1, 3), axis=0)
            lab_std = np.std(lab.reshape(-1, 3), axis=0)
            
            return {
                'dimensions': (width, height),
                'rgb_mean': mean_color.tolist(),
                'rgb_std': color_std.tolist(),
                'hsv_mean': hsv_mean.tolist(),
                'hsv_std': hsv_std.tolist(),
                'lab_mean': lab_mean.tolist(),
                'lab_std': lab_std.tolist(),
                'total_pixels': width * height
            }
            
        except Exception as e:
            logger.warning(f"⚠️ Image feature extraction failed: {e}")
            return {'error': str(e)}

    def _analyze_single_condition(self, condition_id: str, condition_info: Dict, context: Dict) -> Dict:
        """Analyze lesion for a specific dermatological condition using Ollama"""
        try:
            # Create specialized prompt for this condition
            prompt = self._create_condition_specific_prompt(condition_id, condition_info, context)

            # Check if we can actually use Ollama (not just initialized)
            can_use_ollama = (self.initialized and self.ready and
                            hasattr(self, '_ollama_available') and
                            getattr(self, '_ollama_available', False))

            if can_use_ollama:
                # Use REAL Ollama Gemma 3n E4B model - MANDATORY
                analysis = self._run_ollama_analysis(prompt, context)
            else:
                # NO FALLBACKS ALLOWED - CRITICAL ERROR
                raise RuntimeError("❌ CRITICAL: Ollama not available! NO FALLBACKS ALLOWED!")

            # Calculate condition-specific probability
            probability = self._calculate_condition_probability(condition_id, condition_info, context, analysis)

            # Generate condition-specific insights
            insights = self._generate_condition_insights(condition_id, condition_info, context, analysis)

            return {
                'condition_id': condition_id,
                'condition_name': condition_info['name'],
                'probability': probability,
                'confidence': analysis.get('confidence', 0.7),
                'key_features_present': self._identify_present_features(condition_id, condition_info, context),
                'clinical_significance': condition_info['urgency'],
                'insights': insights,
                'ai_analysis': analysis.get('text_analysis', ''),
                'supporting_evidence': self._collect_supporting_evidence(condition_id, context)
            }

        except Exception as e:
            logger.warning(f"⚠️ Single condition analysis failed for {condition_id}: {e}")
            return {
                'condition_id': condition_id,
                'condition_name': condition_info['name'],
                'probability': 0.0,
                'confidence': 0.0,
                'error': str(e)
            }

    def _create_condition_specific_prompt(self, condition_id: str, condition_info: Dict, context: Dict) -> str:
        """Create specialized prompt for specific condition analysis using Gemma 3n E4B multimodal capabilities"""

        # Enhanced multimodal medical context for Gemma 3n E4B
        base_prompt = f"""You are an expert dermatologist AI with advanced multimodal image analysis capabilities, specializing in {condition_info['name']} detection.

MULTIMODAL ANALYSIS TASK:
Analyze the provided skin lesion image using your visual processing capabilities and correlate findings with clinical data to assess {condition_info['name']}.

VISUAL DATA EXTRACTION FOCUS:
- Extract detailed visual features: color patterns, texture, surface characteristics
- Analyze morphological features: asymmetry, border irregularities, size measurements
- Identify specific visual markers associated with {condition_info['name']}
- Assess overall lesion appearance and contextual skin environment

CONDITION INFORMATION:
- Name: {condition_info['name']}
- Type: {condition_info['type']}
- Clinical Urgency: {condition_info['urgency']}
- Key Visual Features to Look For: {', '.join(condition_info['key_features'])}

CLINICAL DATA INTEGRATION:"""

        # Add ABCDE results if available
        if context.get('abcde_results'):
            abcde = context['abcde_results']
            base_prompt += f"""

ABCDE ANALYSIS RESULTS:
- Asymmetry Score: {abcde.get('asymmetry', {}).get('score', 'N/A')}
- Border Irregularity: {abcde.get('border', {}).get('score', 'N/A')}
- Color Variation: {abcde.get('color', {}).get('score', 'N/A')}
- Diameter: {abcde.get('diameter', {}).get('max_diameter_mm', 'N/A')} mm
- Evolution: {abcde.get('evolution', {}).get('score', 'N/A')}"""

        # Add image features
        if context.get('image_features'):
            features = context['image_features']
            base_prompt += f"""

IMAGE CHARACTERISTICS:
- Dimensions: {features.get('dimensions', 'N/A')}
- Average RGB Color: {features.get('rgb_mean', 'N/A')}
- Color Variation (RGB Std): {features.get('rgb_std', 'N/A')}"""

        # Add patient context if available
        if context.get('patient_context'):
            patient = context['patient_context']
            base_prompt += f"""

PATIENT CONTEXT:
- Age: {patient.get('age', 'N/A')}
- Gender: {patient.get('gender', 'N/A')}
- Skin Type: {patient.get('skin_type', 'N/A')}
- Family History: {patient.get('family_history', 'N/A')}
- Previous Skin Cancer: {patient.get('previous_skin_cancer', 'N/A')}"""

        # Enhanced multimodal analysis request
        analysis_request = f"""

MULTIMODAL ANALYSIS REQUEST:
Using your advanced image analysis capabilities, examine the provided skin lesion image and perform comprehensive visual data extraction for {condition_info['name']} assessment.

VISUAL ANALYSIS STEPS:
1. IMAGE DATA EXTRACTION:
   - Analyze color patterns, pigmentation distribution, and chromatic variations
   - Assess texture, surface characteristics, and morphological features
   - Measure asymmetry, border irregularities, and dimensional properties
   - Identify specific visual markers associated with {condition_info['name']}

2. CLINICAL CORRELATION:
   - Compare visual findings with typical {condition_info['name']} presentation
   - Integrate image analysis with provided clinical data (ABCDE scores, patient context)
   - Assess likelihood based on comprehensive multimodal evidence

3. DIAGNOSTIC ASSESSMENT:
   - Determine probability this lesion represents {condition_info['name']}
   - Identify key diagnostic features present or absent in the image
   - Consider differential diagnoses based on visual characteristics

4. CLINICAL RECOMMENDATIONS:
   - Suggest additional evaluation based on visual and clinical findings
   - Provide evidence-based next steps for patient care

RESPONSE FORMAT (JSON-like structure):
Probability: [0.0-1.0]
Confidence: [0.0-1.0]
Visual Features Extracted: [detailed list of observed image characteristics]
Key Diagnostic Features: [features specific to {condition_info['name']}]
Clinical Reasoning: [detailed explanation integrating visual and clinical data]
Recommendations: [evidence-based next steps]
Image Quality Assessment: [assessment of image adequacy for diagnosis]"""

        return base_prompt + analysis_request

    def _run_ollama_analysis(self, prompt: str, context: Dict) -> Dict:
        """Run analysis using Ollama Gemma 3n E4B model with multimodal support"""
        try:
            logger.info(f"🤖 Running Ollama Gemma 3n E4B analysis with {self.timeouts['analysis']}s timeout ({self.timeouts['analysis']//60} minutes)...")
            logger.info("⏳ Deep multimodal medical analysis in progress - this may take up to 1 hour on slow systems...")

            # Prepare request payload
            request_payload = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": self.model_config['temperature'],
                    "top_p": self.model_config['top_p'],
                    "num_predict": self.model_config['num_predict'],
                    "repeat_penalty": self.model_config['repeat_penalty'],
                    "top_k": self.model_config['top_k']
                }
            }

            # Add image data if available and vision is supported
            if self._supports_vision and 'lesion_image' in context and context['lesion_image'] is not None:
                image_base64 = self._prepare_image_for_gemma3n(context['lesion_image'])
                if image_base64:
                    # For Ollama with Gemma 3n E4B, images are included in the request
                    request_payload["images"] = [image_base64]
                    logger.info("🖼️ Including image data in multimodal analysis")

                    # Optimize configuration based on image characteristics
                    if 'enhanced_image_metadata' in context:
                        optimized_config = self._optimize_model_config_for_image(context['enhanced_image_metadata'])
                        request_payload["options"].update(optimized_config)
                        logger.info("🔧 Applied optimized configuration for image analysis")

            # Calculate adaptive timeout if image metadata is available
            timeout_to_use = self.timeouts['analysis']
            if 'enhanced_image_metadata' in context:
                timeout_to_use = self._calculate_adaptive_timeout(context['enhanced_image_metadata'])
                logger.info(f"⏱️ Using adaptive timeout: {timeout_to_use}s")

            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json=request_payload,
                timeout=timeout_to_use
            )

            if response.status_code == 200:
                result = response.json()
                analysis_text = result.get('response', '')

                # Parse the structured response
                parsed_analysis = self._parse_ollama_response(analysis_text)

                return {
                    'text_analysis': analysis_text,
                    'confidence': parsed_analysis.get('confidence', 0.7),
                    'probability': parsed_analysis.get('probability', 0.5),
                    'key_features': parsed_analysis.get('key_features', []),
                    'reasoning': parsed_analysis.get('reasoning', ''),
                    'recommendations': parsed_analysis.get('recommendations', ''),
                    'success': True
                }
            else:
                logger.error(f"❌ Ollama API error: {response.status_code}")
                return {'success': False, 'error': f'API error: {response.status_code}'}

        except requests.exceptions.Timeout:
            logger.error("❌ Ollama multimodal analysis timed out")
            return self._create_multimodal_timeout_result(context)
        except requests.exceptions.ConnectionError:
            logger.error("❌ Connection to Ollama failed during multimodal analysis")
            return self._create_multimodal_connection_error_result(context)
        except Exception as e:
            error_msg = str(e)
            if "image" in error_msg.lower() or "multimodal" in error_msg.lower():
                logger.error(f"❌ Multimodal analysis failed: {e}")
                return self._create_multimodal_error_result(error_msg, context)
            else:
                logger.error(f"❌ Ollama analysis failed: {e}")
                return {'success': False, 'error': error_msg}

    def _parse_ollama_response(self, response_text: str) -> Dict:
        """Parse structured response from Ollama"""
        try:
            parsed = {
                'probability': 0.5,
                'confidence': 0.7,
                'key_features': [],
                'reasoning': '',
                'recommendations': ''
            }

            lines = response_text.split('\n')
            current_section = None

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Look for probability
                if line.lower().startswith('probability:'):
                    try:
                        prob_str = line.split(':', 1)[1].strip()
                        # Extract number from string
                        import re
                        prob_match = re.search(r'(\d+\.?\d*)', prob_str)
                        if prob_match:
                            parsed['probability'] = float(prob_match.group(1))
                            if parsed['probability'] > 1.0:
                                parsed['probability'] = parsed['probability'] / 100.0
                    except:
                        pass

                # Look for key features
                elif line.lower().startswith('key features'):
                    current_section = 'features'
                elif line.lower().startswith('clinical reasoning'):
                    current_section = 'reasoning'
                elif line.lower().startswith('recommendations'):
                    current_section = 'recommendations'
                elif current_section == 'reasoning':
                    parsed['reasoning'] += line + ' '
                elif current_section == 'recommendations':
                    parsed['recommendations'] += line + ' '
                elif current_section == 'features' and line.startswith('-'):
                    parsed['key_features'].append(line[1:].strip())

            # Clean up text
            parsed['reasoning'] = parsed['reasoning'].strip()
            parsed['recommendations'] = parsed['recommendations'].strip()

            return parsed

        except Exception as e:
            logger.warning(f"⚠️ Response parsing failed: {e}")
            return {
                'probability': 0.5,
                'confidence': 0.5,
                'key_features': [],
                'reasoning': response_text[:200] + '...' if len(response_text) > 200 else response_text,
                'recommendations': 'Further evaluation recommended'
            }

    def _create_multimodal_timeout_result(self, context: Dict) -> Dict:
        """Create result for multimodal analysis timeout - NO FALLBACKS"""
        raise RuntimeError(
            "❌ CRITICAL: Multimodal analysis timeout! "
            "The REAL Gemma 3n E4B model timed out. "
            "NO FALLBACKS ALLOWED! "
            "Please check system resources and retry."
        )

    def _create_multimodal_connection_error_result(self, context: Dict) -> Dict:
        """Create result for multimodal connection error - NO FALLBACKS"""
        raise RuntimeError(
            "❌ CRITICAL: Connection error during multimodal analysis! "
            "Cannot connect to Ollama with REAL Gemma 3n E4B model. "
            "NO FALLBACKS ALLOWED! "
            "Please verify Ollama is running and accessible."
        )

    def _create_multimodal_error_result(self, error_msg: str, context: Dict) -> Dict:
        """Create result for general multimodal error - NO FALLBACKS"""
        raise RuntimeError(
            f"❌ CRITICAL: Multimodal analysis error with REAL Gemma 3n E4B: {error_msg}. "
            "NO FALLBACKS ALLOWED! "
            "Please check image format, quality, and system configuration."
        )

    def _run_gemma_analysis(self, prompt: str, context: Dict) -> Dict:
        """Run analysis using the actual Gemma model"""
        try:
            # Tokenize prompt
            inputs = self.tokenizer(
                prompt,
                return_tensors="pt",
                truncation=True,
                max_length=self.model_config['max_length'] // 2  # Leave room for response
            )

            # Move to device if not using 8-bit
            if not self.model_config['use_8bit']:
                inputs = {k: v.to(self.device) for k, v in inputs.items()}

            # Generate response
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_length=self.model_config['max_length'],
                    temperature=self.model_config['temperature'],
                    top_p=self.model_config['top_p'],
                    do_sample=self.model_config['do_sample'],
                    pad_token_id=self.model_config['pad_token_id'],
                    eos_token_id=self.tokenizer.eos_token_id
                )

            # Decode response
            full_response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)

            # Extract only the generated part (after the prompt)
            generated_text = full_response[len(prompt):].strip()

            # Parse the structured response
            parsed_analysis = self._parse_gemma_response(generated_text)

            return {
                'text_analysis': generated_text,
                'parsed_analysis': parsed_analysis,
                'confidence': 0.8,  # High confidence for actual model
                'method': 'gemma_3n'
            }

        except Exception as e:
            logger.warning(f"⚠️ Gemma analysis failed: {e}")
            return {
                'text_analysis': f"Analysis failed: {str(e)}",
                'confidence': 0.0,
                'method': 'error'
            }

    # FALLBACK ANALYSIS METHODS COMPLETELY REMOVED - NO FALLBACKS ALLOWED
    # This application requires REAL Gemma 3n E4B model to function

    def _parse_gemma_response(self, response_text: str) -> Dict:
        """Parse structured response from Gemma model"""
        try:
            parsed = {}

            # Extract probability
            prob_match = None
            for line in response_text.split('\n'):
                if 'probability' in line.lower() or 'likelihood' in line.lower():
                    # Look for numbers between 0 and 1
                    import re
                    numbers = re.findall(r'0\.\d+|1\.0|0\.0', line)
                    if numbers:
                        parsed['probability'] = float(numbers[0])
                        break

            # Extract key features
            features = []
            in_features_section = False
            for line in response_text.split('\n'):
                if 'key features' in line.lower() or 'features present' in line.lower():
                    in_features_section = True
                    continue
                if in_features_section and line.strip():
                    if line.startswith('-') or line.startswith('•'):
                        features.append(line.strip()[1:].strip())
                    elif ':' in line:
                        break

            parsed['key_features'] = features

            # Extract clinical reasoning
            reasoning_lines = []
            in_reasoning = False
            for line in response_text.split('\n'):
                if 'clinical reasoning' in line.lower() or 'reasoning' in line.lower():
                    in_reasoning = True
                    continue
                if in_reasoning and line.strip():
                    if 'recommendation' in line.lower():
                        break
                    reasoning_lines.append(line.strip())

            parsed['clinical_reasoning'] = ' '.join(reasoning_lines)

            return parsed

        except Exception as e:
            logger.warning(f"⚠️ Response parsing failed: {e}")
            return {'parsing_error': str(e)}

    # FALLBACK ANALYSIS COMPLETELY REMOVED - NO FALLBACKS ALLOWED
    # This application requires REAL Gemma 3n E4B model to function

    def _create_error_result(self, error_message: str) -> Dict:
        """Create error result structure"""
        return {
            'timestamp': datetime.now().isoformat(),
            'processing_time': 0.0,
            'condition_analyses': {},
            'overall_assessment': {
                'most_likely_condition': 'unknown',
                'confidence': 0.0,
                'risk_level': 'unknown',
                'requires_followup': True
            },
            'clinical_recommendations': [
                "Analysis failed - professional evaluation required",
                error_message
            ],
            'confidence_metrics': {
                'overall_confidence': 0.0,
                'analysis_method': 'error'
            },
            'success': False,
            'error': error_message
        }

    def _update_analysis_stats(self, results: Dict) -> None:
        """Update analysis statistics (placeholder)"""
        # This could track analysis performance, condition frequencies, etc.
        pass

    def _generate_overall_assessment(self, condition_analyses: Dict, abcde_results: Optional[Dict]) -> Dict:
        """Generate overall assessment from condition analyses"""
        try:
            # Find condition with highest probability
            max_prob = 0.0
            max_condition = 'healthy'
            max_confidence = 0.0

            for condition_id, analysis in condition_analyses.items():
                prob = analysis.get('probability', 0.0)
                if prob > max_prob:
                    max_prob = prob
                    max_condition = condition_id
                    max_confidence = analysis.get('confidence', 0.0)

            # Determine risk level
            if max_prob >= 0.7:
                risk_level = 'high'
            elif max_prob >= 0.4:
                risk_level = 'medium'
            else:
                risk_level = 'low'

            return {
                'most_likely_condition': max_condition,
                'confidence': max_confidence,
                'risk_level': risk_level,
                'max_probability': max_prob,
                'requires_followup': max_prob > 0.3
            }

        except Exception as e:
            logger.warning(f"⚠️ Overall assessment generation failed: {e}")
            return {
                'most_likely_condition': 'unknown',
                'confidence': 0.0,
                'risk_level': 'unknown',
                'requires_followup': True
            }

    def _generate_clinical_recommendations(self, condition_analyses: Dict, overall_assessment: Dict, patient_context: Optional[Dict]) -> List[str]:
        """Generate clinical recommendations based on analysis"""
        try:
            recommendations = []

            risk_level = overall_assessment.get('risk_level', 'unknown')
            most_likely = overall_assessment.get('most_likely_condition', 'unknown')

            # Risk-based recommendations
            if risk_level == 'high':
                recommendations.append("🔴 HIGH RISK: Urgent dermatologist consultation recommended within 1-2 weeks")
                recommendations.append("Monitor for any changes in size, color, or symptoms")
            elif risk_level == 'medium':
                recommendations.append("🟡 MEDIUM RISK: Dermatologist evaluation recommended within 4-6 weeks")
                recommendations.append("Continue regular skin self-examinations")
            else:
                recommendations.append("🟢 LOW RISK: Routine monitoring recommended")
                recommendations.append("Annual dermatological check-up advised")

            # Condition-specific recommendations
            if most_likely == 'melanoma':
                recommendations.append("Melanoma suspected - immediate professional evaluation required")
            elif most_likely in ['basal_cell_carcinoma', 'squamous_cell_carcinoma']:
                recommendations.append("Skin cancer suspected - professional evaluation required")
            elif most_likely in ['chickenpox', 'measles', 'monkeypox']:
                recommendations.append("Infectious disease suspected - medical evaluation and isolation may be required")

            # General recommendations
            recommendations.append("Protect skin from UV exposure with sunscreen and protective clothing")
            recommendations.append("Perform regular skin self-examinations")

            return recommendations

        except Exception as e:
            logger.warning(f"⚠️ Clinical recommendations generation failed: {e}")
            return ["Professional dermatological evaluation recommended"]

    def _calculate_confidence_metrics(self, condition_analyses: Dict) -> Dict:
        """Calculate confidence metrics for the analysis"""
        try:
            confidences = [analysis.get('confidence', 0.0) for analysis in condition_analyses.values()]

            return {
                'overall_confidence': np.mean(confidences) if confidences else 0.0,
                'confidence_std': np.std(confidences) if confidences else 0.0,
                'min_confidence': min(confidences) if confidences else 0.0,
                'max_confidence': max(confidences) if confidences else 0.0,
                'analysis_method': 'ollama_gemma3n' if self.ready else 'fallback'
            }

        except Exception as e:
            logger.warning(f"⚠️ Confidence metrics calculation failed: {e}")
            return {
                'overall_confidence': 0.0,
                'analysis_method': 'error'
            }

    def _identify_present_features(self, condition_id: str, condition_info: Dict, context: Dict) -> List[str]:
        """Identify features present in the lesion for a specific condition"""
        try:
            present_features = []

            # Get lesion characteristics from context
            lesion_image = context.get('lesion_image')
            lesion_metadata = context.get('lesion_metadata', {})

            # Basic feature detection based on condition
            if condition_id == 'melanoma':
                # Check for melanoma features
                if lesion_metadata.get('area', 0) > 1000:
                    present_features.append('Large size')
                if lesion_metadata.get('confidence', 0) > 0.7:
                    present_features.append('Distinct borders')
                present_features.append('Pigmented lesion')

            elif condition_id == 'basal_cell_carcinoma':
                present_features.append('Raised lesion')
                present_features.append('Pearly appearance')

            elif condition_id == 'squamous_cell_carcinoma':
                present_features.append('Scaly texture')
                present_features.append('Irregular surface')

            elif condition_id in ['chickenpox', 'measles', 'monkeypox']:
                present_features.append('Multiple lesions')
                present_features.append('Vesicular appearance')

            elif condition_id == 'actinic_keratoses':
                present_features.append('Rough texture')
                present_features.append('Sun-exposed area')

            elif condition_id == 'healthy':
                present_features.append('Normal skin texture')
                present_features.append('Uniform coloration')

            else:
                # Generic features for other conditions
                present_features.append('Visible lesion')
                present_features.append('Color variation')

            return present_features

        except Exception as e:
            logger.warning(f"⚠️ Feature identification failed for {condition_id}: {e}")
            return ['Analysis incomplete']

    def _calculate_condition_probability(self, condition_id: str, condition_info: Dict, context: Dict, analysis: Dict) -> float:
        """Calculate probability for a specific condition"""
        try:
            # Base probability from condition priority (inverse relationship)
            priority = condition_info.get('priority', 10)
            base_prob = max(0.1, 1.0 - (priority / 15.0))  # Higher priority = higher base prob

            # Get lesion characteristics
            lesion_metadata = context.get('lesion_metadata', {})
            lesion_area = lesion_metadata.get('area', 0)
            lesion_confidence = lesion_metadata.get('confidence', 0.5)

            # Condition-specific probability adjustments
            probability_modifier = 1.0

            if condition_id == 'melanoma':
                # Higher probability for larger, more distinct lesions
                if lesion_area > 2000:
                    probability_modifier *= 1.5
                if lesion_confidence > 0.8:
                    probability_modifier *= 1.3

            elif condition_id == 'healthy':
                # Lower probability if lesion is very distinct
                if lesion_confidence > 0.7:
                    probability_modifier *= 0.5
                if lesion_area > 1000:
                    probability_modifier *= 0.7

            elif condition_id in ['basal_cell_carcinoma', 'squamous_cell_carcinoma']:
                # Moderate probability for skin cancers
                if lesion_area > 500:
                    probability_modifier *= 1.2

            elif condition_id in ['chickenpox', 'measles', 'monkeypox']:
                # Lower probability unless specific features present
                probability_modifier *= 0.8

            # Apply analysis confidence if available
            analysis_confidence = analysis.get('confidence', 0.5)
            probability_modifier *= analysis_confidence

            # Calculate final probability
            final_probability = base_prob * probability_modifier

            # Ensure probability is within valid range
            return min(0.95, max(0.05, final_probability))

        except Exception as e:
            logger.warning(f"⚠️ Probability calculation failed for {condition_id}: {e}")
            return 0.1  # Default low probability

    def _generate_condition_insights(self, condition_id: str, condition_info: Dict, context: Dict, analysis: Dict) -> List[str]:
        """Generate clinical insights for a specific condition"""
        try:
            insights = []

            # Get analysis data
            probability = analysis.get('probability', 0.0)
            confidence = analysis.get('confidence', 0.5)

            # Get lesion characteristics
            lesion_metadata = context.get('lesion_metadata', {})
            lesion_area = lesion_metadata.get('area', 0)
            lesion_confidence = lesion_metadata.get('confidence', 0.5)

            # General insights based on probability
            if probability > 0.7:
                insights.append(f"High probability ({probability:.1%}) of {condition_info['name']}")
                insights.append("Immediate professional evaluation recommended")
            elif probability > 0.4:
                insights.append(f"Moderate probability ({probability:.1%}) of {condition_info['name']}")
                insights.append("Professional evaluation advised within 2-4 weeks")
            else:
                insights.append(f"Low probability ({probability:.1%}) of {condition_info['name']}")
                insights.append("Continue monitoring with regular self-examinations")

            # Condition-specific insights
            if condition_id == 'melanoma':
                if lesion_area > 2000:
                    insights.append("Large lesion size increases melanoma concern")
                if lesion_confidence > 0.8:
                    insights.append("Well-defined lesion borders noted")
                insights.append("ABCDE criteria evaluation recommended")

            elif condition_id in ['basal_cell_carcinoma', 'squamous_cell_carcinoma']:
                insights.append("Non-melanoma skin cancer consideration")
                if lesion_area > 1000:
                    insights.append("Lesion size warrants professional evaluation")
                insights.append("Early detection improves treatment outcomes")

            elif condition_id in ['chickenpox', 'measles', 'monkeypox']:
                insights.append("Infectious disease pattern detected")
                insights.append("Consider isolation and medical consultation")
                insights.append("Monitor for systemic symptoms")

            elif condition_id == 'actinic_keratoses':
                insights.append("Precancerous lesion - sun damage related")
                insights.append("Regular dermatological monitoring essential")
                insights.append("Sun protection measures recommended")

            elif condition_id == 'healthy':
                insights.append("Normal skin appearance")
                insights.append("Continue regular skin self-examinations")
                insights.append("Annual dermatological check-up advised")

            # Confidence-based insights
            if confidence < 0.5:
                insights.append("Analysis confidence is moderate - professional opinion recommended")
            elif confidence > 0.8:
                insights.append("High confidence in analysis results")

            # Priority-based insights
            priority = condition_info.get('priority', 10)
            if priority <= 3:
                insights.append("High-priority condition requiring urgent attention")
            elif priority <= 6:
                insights.append("Moderate-priority condition requiring timely evaluation")

            return insights[:5]  # Limit to 5 most relevant insights

        except Exception as e:
            logger.warning(f"⚠️ Insight generation failed for {condition_id}: {e}")
            return [f"Analysis completed for {condition_info.get('name', condition_id)}", "Professional evaluation recommended"]

    def _collect_supporting_evidence(self, condition_id: str, context: Dict) -> List[str]:
        """Collect supporting evidence for condition diagnosis"""
        try:
            evidence = []

            # Get lesion characteristics
            lesion_metadata = context.get('lesion_metadata', {})
            lesion_area = lesion_metadata.get('area', 0)
            lesion_confidence = lesion_metadata.get('confidence', 0.5)

            # Basic evidence
            if lesion_area > 0:
                evidence.append(f"Lesion area: {lesion_area} pixels")
            if lesion_confidence > 0:
                evidence.append(f"Detection confidence: {lesion_confidence:.1%}")

            # Condition-specific evidence
            if condition_id == 'melanoma':
                evidence.append("Pigmented lesion detected")
                if lesion_area > 1500:
                    evidence.append("Large lesion size (>6mm equivalent)")

            elif condition_id in ['basal_cell_carcinoma', 'squamous_cell_carcinoma']:
                evidence.append("Non-pigmented lesion characteristics")
                evidence.append("Irregular surface texture noted")

            elif condition_id in ['chickenpox', 'measles', 'monkeypox']:
                evidence.append("Vesicular lesion pattern")
                evidence.append("Multiple lesion distribution")

            elif condition_id == 'healthy':
                evidence.append("Normal skin coloration")
                evidence.append("Regular surface texture")

            # ABCDE evidence if available
            abcde_results = context.get('abcde_analysis', {})
            if abcde_results:
                if abcde_results.get('asymmetry_score', 0) > 0.5:
                    evidence.append("Asymmetry detected")
                if abcde_results.get('border_irregularity_score', 0) > 0.5:
                    evidence.append("Border irregularity noted")
                if abcde_results.get('color_variation_score', 0) > 0.5:
                    evidence.append("Color variation present")

            return evidence[:4]  # Limit to 4 pieces of evidence

        except Exception as e:
            logger.warning(f"⚠️ Evidence collection failed for {condition_id}: {e}")
            return ["Clinical analysis performed", "Professional evaluation recommended"]
