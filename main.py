#!/usr/bin/env python3
"""
🏥 DermatoGemma Multi-Detection System v2.0
Main Application Entry Point - Revolutionary Medical AI Interface

Revolutionary AI-powered multi-condition dermatological screening system
using Google Gemma 3n with modern UI/UX following RetinoblastoGemma-App patterns.

Features:
- Modern Professional UI/UX
- Gemma Model Management
- Automatic Dependency Installation
- Professional Medical Interface
- Complete Analysis Engine

Author: DermatoGemma Development Team
Version: 2.0 - Modern UI Edition
License: Medical Research License
"""

# Import warning suppression module FIRST
import suppress_warnings

import sys
import tkinter as tk
from tkinter import messagebox
import logging
from pathlib import Path
import traceback
from datetime import datetime
import argparse
import subprocess
from typing import Optional, Tuple, List

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Apply warning suppressions and optimizations
try:
    from fix_warnings import apply_runtime_fixes
    apply_runtime_fixes()
except ImportError:
    # Fallback: Apply basic warning suppressions
    import warnings
    warnings.filterwarnings("ignore", category=DeprecationWarning, module="paramiko")
    warnings.filterwarnings("ignore", message=".*TripleDES.*", category=DeprecationWarning)
    warnings.filterwarnings("ignore", message=".*Blowfish.*", category=DeprecationWarning)
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
    os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'

# Configure logging
log_dir = project_root / "logs"
log_dir.mkdir(exist_ok=True)

# Configure logging with UTF-8 encoding for Windows compatibility
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(
            log_dir / f"dermatogamma_{datetime.now().strftime('%Y%m%d')}.log",
            encoding='utf-8'
        ),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class DermatoGemmaMain:
    """
    Main application controller with modern UI integration
    Following RetinoblastoGemma-App patterns for professional medical interface
    """
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.models_dir = self.project_root / "models"
        
        logger.info("🏥 DermatoGemma Multi-Detection System v2.0 - Modern UI Edition")
        logger.info("🎨 Professional Medical Interface with Gemma Model Management")
        
    def check_python_version(self) -> bool:
        """Check Python version compatibility"""
        if sys.version_info < (3, 8):
            logger.error("❌ Python 3.8 or higher is required")
            logger.error(f"Current version: {sys.version_info.major}.{sys.version_info.minor}")
            return False
        
        logger.info(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
        return True
    
    def check_essential_dependencies(self) -> Tuple[bool, List[str]]:
        """Check if essential dependencies are available"""
        essential_packages = [
            ("customtkinter", "Modern UI components"),
            ("PIL", "Image processing (Pillow)"),
            ("numpy", "Numerical computing"),
            ("cv2", "Computer vision (opencv-python)")
        ]
        
        missing_packages = []
        available_packages = []
        
        for package, description in essential_packages:
            try:
                if package == "PIL":
                    import PIL
                elif package == "cv2":
                    import cv2
                else:
                    __import__(package)
                
                logger.info(f"✅ {package} - {description}")
                available_packages.append(package)
                
            except ImportError:
                logger.warning(f"⚠️ {package} - {description} (MISSING)")
                missing_packages.append(package)
        
        return len(missing_packages) == 0, missing_packages
    
    def launch_modern_ui(self) -> bool:
        """Launch the modern UI interface"""
        try:
            logger.info("🎨 Launching Modern DermatoGemma UI...")
            
            # Import modern UI
            from ui_modern import ModernDermatoGemmaUI
            
            # Create and run the modern application
            app = ModernDermatoGemmaUI()
            app.run()
            
            return True
            
        except ImportError as e:
            logger.error(f"❌ Modern UI import failed: {e}")
            logger.info("💡 Try installing dependencies: python install_ui_dependencies.py")
            return False
        except Exception as e:
            logger.error(f"❌ Modern UI launch failed: {e}")
            return False
    
    def launch_professional_launcher(self) -> bool:
        """Launch the professional launcher"""
        try:
            logger.info("🚀 Launching Professional Launcher...")
            
            launcher_path = self.project_root / "launcher.py"
            if launcher_path.exists():
                subprocess.run([sys.executable, str(launcher_path)])
                return True
            else:
                logger.error("❌ Professional launcher not found")
                return False
                
        except Exception as e:
            logger.error(f"❌ Launcher failed: {e}")
            return False
    
    def launch_classic_ui(self) -> bool:
        """Launch classic UI as fallback"""
        try:
            logger.info("🔄 Launching Classic UI (fallback)...")
            
            # Try different classic UI files
            classic_files = ["main_windows.py", "main_simple.py"]
            
            for ui_file in classic_files:
                try:
                    ui_path = self.project_root / ui_file
                    if ui_path.exists():
                        subprocess.run([sys.executable, str(ui_path)])
                        return True
                except Exception:
                    continue
            
            logger.error("❌ No classic UI available")
            return False
            
        except Exception as e:
            logger.error(f"❌ Classic UI launch failed: {e}")
            return False
    
    def run(self, mode: str = "auto") -> bool:
        """
        Main run method with different modes
        
        Args:
            mode: "auto", "modern", "launcher", "classic"
        """
        try:
            logger.info("=" * 80)
            logger.info("🏥 DermatoGemma Multi-Detection System v2.0 - Starting...")
            logger.info("=" * 80)
            
            # Check Python version
            if not self.check_python_version():
                return False
            
            # Check dependencies for UI modes
            deps_ok, missing = self.check_essential_dependencies()
            
            if not deps_ok:
                logger.warning(f"⚠️ Missing dependencies: {', '.join(missing)}")
                logger.info("💡 Install with: python install_ui_dependencies.py")
                return self.launch_classic_ui()
            
            # Launch appropriate interface based on mode
            if mode == "modern":
                return self.launch_modern_ui()
            elif mode == "launcher":
                return self.launch_professional_launcher()
            elif mode == "classic":
                return self.launch_classic_ui()
            else:  # auto mode
                # Try modern UI first, fallback to others
                if deps_ok:
                    logger.info("🎨 Auto mode: Launching Modern UI (recommended)")
                    return self.launch_modern_ui()
                else:
                    logger.info("🔄 Falling back to classic UI")
                    return self.launch_classic_ui()
                
        except KeyboardInterrupt:
            logger.info("⚠️ Application interrupted by user")
            return False
        except Exception as e:
            logger.error(f"❌ Application error: {e}")
            logger.error(traceback.format_exc())
            return False

def main():
    """
    Main entry point with command line argument support
    
    Usage:
        python main.py                    # Auto mode (modern UI preferred)
        python main.py --modern           # Force modern UI
        python main.py --launcher         # Professional launcher
        python main.py --classic          # Classic UI
    """
    try:
        # Parse command line arguments
        parser = argparse.ArgumentParser(
            description="DermatoGemma Multi-Detection System v2.0 - Revolutionary Medical AI",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  python main.py                           # Launch with auto-detection
  python main.py --modern                  # Force modern UI
  python main.py --launcher                # Professional launcher
            """
        )
        
        parser.add_argument("--modern", action="store_true",
                          help="Launch modern UI interface")
        parser.add_argument("--launcher", action="store_true",
                          help="Launch professional launcher")
        parser.add_argument("--classic", action="store_true",
                          help="Launch classic UI interface")
        parser.add_argument("--fast", action="store_true",
                          help="Fast startup with lazy model loading")
        parser.add_argument("--version", action="version",
                          version="DermatoGemma Multi-Detection System v2.0")
        
        args = parser.parse_args()
        
        # Create main application
        app = DermatoGemmaMain()
        
        # Determine mode
        if args.modern:
            mode = "modern"
        elif args.launcher:
            mode = "launcher"
        elif args.classic:
            mode = "classic"
        else:
            mode = "auto"
        
        # Run application
        success = app.run(mode=mode)
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        logger.info("🛑 Application interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Critical application error: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
