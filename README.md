# 🏥 DermatoGemma Multi-Detection System v2.0 - Ollama Edition

Revolutionary AI-powered dermatological screening system using Google Gemma3n:e2b via Ollama for comprehensive multi-condition skin lesion analysis.

## 🎯 Key Features

- **14 Target Diseases**: Focused detection of specific dermatological conditions
- **Ollama Integration**: Local AI processing via Ollama for maximum privacy
- **ABCDE Integration**: Advanced melanoma screening using clinical ABCDE criteria
- **Modern UI/UX**: Professional medical interface with real-time analysis
- **Local Processing**: Complete privacy with local AI processing
- **Clinical Integration**: Structured reports for medical professionals
- **Simplified Setup**: Easy installation with Ollama backend

## 🔬 Supported Conditions (14 Target Diseases)

1. **Actinic Keratoses** (Precancerous)
2. **Basal Cell Carcinoma** (Malignant)
3. **Benign Keratosis-like Lesions** (Benign)
4. **Chickenpox** (Viral Infection)
5. **Cowpox** (Viral Infection)
6. **Dermatofibroma** (Benign)
7. **Healthy Skin** (Normal)
8. **HFMD** (Hand, Foot and Mouth Disease)
9. **Measles** (Viral Infection)
10. **Melanocytic Nevi** (Benign)
11. **Melanoma** (Malignant - High Priority)
12. **Monkeypox** (Viral Infection)
13. **Squamous Cell Carcinoma** (Malignant)
14. **Vascular Lesions** (Benign)

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- 4GB RAM minimum (8GB recommended)
- Ollama installed and running
- Gemma3n:e2b model available in Ollama

### Installation

1. **Install Ollama**
   - Download from: https://ollama.ai/
   - Install and start Ollama service

2. **Pull Gemma3n:e2b model**
   ```bash
   ollama pull gemma3n:e2b
   ```

3. **Start Ollama service**
   ```bash
   ollama serve
   ```

4. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd DermatoGemma-MultiDetection
   ```

5. **Create virtual environment**
   ```bash
   python -m venv dermatogamma_env
   source dermatogamma_env/bin/activate  # Linux/Mac
   # or
   dermatogamma_env\Scripts\activate     # Windows
   ```

6. **Install dependencies**
   ```bash
   pip install -r requirements_ollama.txt
   ```

7. **Launch the application**
   ```bash
   python main.py
   ```

## 🎨 User Interface Options

### Modern UI (Recommended)
```bash
python main.py --modern
```
- Professional medical interface
- Real-time analysis visualization
- Advanced result presentation

### Auto Mode (Default)
```bash
python main.py
```
- Automatically selects best available interface
- Falls back gracefully if dependencies missing

## 📊 Analysis Workflow

1. **Image Upload**: Load dermatological images (JPEG, PNG)
2. **Lesion Detection**: Automatic identification of skin lesions
3. **Multi-Condition Analysis**: Simultaneous evaluation for all 14 target diseases
4. **ABCDE Assessment**: Specialized melanoma screening
5. **Risk Stratification**: Clinical priority classification
6. **Report Generation**: Comprehensive medical reports

## 🔧 Configuration

The system is configured via `config.json`:
- **Ollama Settings**: URL, model, and EXTREMELY generous timeouts for low-power systems
  - Connection check: 60 seconds (1 minute)
  - Model test: 1800 seconds (30 minutes)
  - Full analysis: 3600 seconds (60 minutes / 1 hour)
  - Simple queries: 900 seconds (15 minutes)
- **Target Diseases**: 14 specific conditions
- **Analysis Parameters**: Thresholds and options
- **UI Preferences**: Theme and display options

## 📁 Project Structure

```
DermatoGemma-MultiDetection/
├── main.py                    # Main application entry
├── ui_modern.py              # Modern UI interface
├── config.json               # System configuration
├── requirements_ollama.txt   # Simplified dependencies
├── core/                     # Core analysis modules
│   ├── multi_condition_engine.py
│   ├── gemma_derma_handler.py
│   ├── abcde_analyzer.py
│   └── skin_detector_v2.py
├── data/                     # Reference data
├── test_images/              # Sample images
├── results/                  # Analysis results
└── logs/                     # System logs
```

## 🏥 Medical Compliance

- **Research Use Only**: Not for primary diagnosis
- **HIPAA Compliant**: Local processing ensures privacy
- **Clinical Guidelines**: Follows dermatological standards
- **Professional Oversight**: Requires physician interpretation

## 🔬 Technical Specifications

### AI Backend
- **Model**: Google Gemma3n:e2b via Ollama
- **Processing**: Local inference through Ollama API
- **Memory**: Managed by Ollama (reduced system requirements)
- **Privacy**: Complete local processing

### Performance
- **Analysis Time**: 30-90 seconds per image
- **Accuracy**: Clinical-grade screening performance
- **Memory Usage**: 2-4GB (managed by Ollama)
- **GPU Support**: Handled by Ollama automatically

## 🛠️ Troubleshooting

### Common Issues

1. **Ollama Connection Failed**
   ```bash
   # Check if Ollama is running
   ollama list
   
   # Start Ollama if not running
   ollama serve
   ```

2. **Model Not Available**
   ```bash
   # Pull the required model
   ollama pull gemma3n:e2b
   ```

3. **UI Not Loading**
   ```bash
   pip install customtkinter pillow
   python main.py --classic
   ```

4. **Analysis Timeout**
   - System uses EXTREMELY generous timeouts (up to 1 hour for analysis)
   - **Low-power systems**: Analysis may take 30-60 minutes - this is normal
   - Check Ollama service status: `ollama ps`
   - Verify model is loaded: `ollama list`
   - **Be patient**: Large models need significant time on slower hardware
   - For even slower systems, increase timeouts further in config.json

### Performance Optimization

- **Ollama Configuration**: Adjust Ollama settings for your hardware
- **SSD Storage**: Use SSD for better I/O performance
- **Memory**: 8GB RAM recommended for optimal performance

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Implement changes with tests
4. Submit pull request

## 📄 License

Medical Research License - See LICENSE file for details.

## 🆘 Support

- **Issues**: GitHub Issues
- **Documentation**: Wiki pages
- **Community**: Discussions forum

## 🔄 Version History

- **v2.0-Ollama**: Ollama integration, 14 target diseases, simplified setup
- **v2.0**: Modern UI, multi-condition analysis, ABCDE integration
- **v1.5**: Gemma 3n integration, performance improvements
- **v1.0**: Initial release with basic detection

---

**⚠️ Medical Disclaimer**: This software is for research and screening purposes only. Always consult qualified medical professionals for diagnosis and treatment decisions.
