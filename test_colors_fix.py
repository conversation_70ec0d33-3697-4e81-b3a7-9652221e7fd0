#!/usr/bin/env python3
"""
Test color fix for DermatoGemma Multi-Detection System v2.0 - Ollama Edition
"""

# Import warning suppression module FIRST
import suppress_warnings

import sys
from pathlib import Path
import tkinter as tk
import customtkinter as ctk

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_color_definitions():
    """Test that all required colors are defined"""
    print("🧪 Testing color definitions...")
    
    try:
        # Configure CustomTkinter
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # Create a minimal GUI instance to test colors
        root = ctk.CTk()
        root.title("Color Test")
        root.geometry("400x300")
        
        # Define colors (same as in ui_modern.py)
        colors = {
            'primary': '#2E86AB',      # Medical blue
            'secondary': '#A23B72',    # Accent purple
            'success': '#28A745',      # Success green
            'warning': '#FFC107',      # Warning yellow
            'danger': '#C73E1D',       # Alert red
            'error': '#C73E1D',        # Error red (alias for danger)
            'background': '#F8F9FA',   # Clean white
            'surface': '#FFFFFF',      # Pure white
            'text_primary': '#212529', # Dark text
            'text_secondary': '#6C757D', # Gray text
            'border': '#DEE2E6'        # Light border
        }
        
        # Test all color references that were causing errors
        required_colors = ['success', 'warning', 'danger', 'error', 'text_secondary']
        
        print("   - Testing color definitions:")
        for color_name in required_colors:
            if color_name in colors:
                print(f"     ✅ {color_name}: {colors[color_name]}")
            else:
                print(f"     ❌ {color_name}: MISSING")
                return False
        
        # Test color usage in risk assessment
        print("   - Testing risk color coding:")
        for risk_level in ['LOW', 'MODERATE', 'HIGH', 'UNKNOWN']:
            risk_color = colors['success'] if risk_level == 'LOW' else \
                        colors['warning'] if risk_level == 'MODERATE' else \
                        colors['danger'] if risk_level == 'HIGH' else \
                        colors['text_secondary']
            print(f"     ✅ {risk_level}: {risk_color}")
        
        # Test disclaimer frame color
        print("   - Testing disclaimer frame:")
        disclaimer_color = colors['warning']
        print(f"     ✅ Disclaimer frame: {disclaimer_color}")
        
        # Create test widgets to ensure colors work
        test_frame = ctk.CTkFrame(root, fg_color=colors['warning'])
        test_frame.pack(pady=10)
        
        test_label = ctk.CTkLabel(
            test_frame,
            text="Color test successful!",
            text_color="black"
        )
        test_label.pack(pady=10)
        
        # Update GUI
        root.update()
        
        print("✅ Color definitions test completed successfully!")
        print("   - All required colors are defined")
        print("   - Risk color coding works correctly")
        print("   - Disclaimer frame color is valid")
        print("   - Test widgets created without errors")
        
        # Don't start mainloop for automated testing
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Color definitions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_methods_with_colors():
    """Test GUI methods that use colors"""
    print("🧪 Testing GUI methods with color usage...")
    
    try:
        from ui_modern import ModernDermatoGemmaUI
        
        # Create GUI instance (this will test color definitions)
        app = ModernDermatoGemmaUI()
        
        # Create mock results to test color usage
        mock_results = {
            'success': True,
            'timestamp': '2025-07-27T14:30:00.000000',
            'processing_time': 12.5,
            'no_lesions_detected': False,
            'analysis_metadata': {
                'total_lesions_detected': 2,
                'lesions_analyzed': 2
            },
            'overall_assessment': {
                'overall_risk_level': 'moderate',
                'risk_distribution': {'high': 0, 'medium': 1, 'low': 1},
                'condition_detections': {
                    'melanocytic_nevi': 1,
                    'benign_keratosis_like_lesions': 1
                }
            },
            'risk_assessment': {
                'clinical_urgency': 'soon',
                'follow_up_recommended': True
            },
            'clinical_recommendations': [
                "Schedule dermatologist appointment within 4-6 weeks",
                {'recommendation': "Monitor lesion for changes", 'priority': 'high'},
                "Take photographs for comparison",
                "Use sun protection measures"
            ],
            'confidence_metrics': {
                'overall_confidence': 0.82,
                'analysis_quality': 'high'
            }
        }
        
        # Set mock results
        app.analysis_results = mock_results
        
        # Test display_results method (this was causing the KeyError)
        print("   - Testing display_results method...")
        app.display_results()
        
        print("✅ GUI methods with colors test completed successfully!")
        print("   - display_results() executed without KeyError")
        print("   - All color references resolved correctly")
        print("   - Risk assessment color coding works")
        print("   - Disclaimer frame created successfully")
        
        # Don't start mainloop for automated testing
        app.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI methods with colors test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run color fix tests"""
    print("🏥 DermatoGemma Multi-Detection System v2.0 - Ollama Edition")
    print("🧪 Testing color definitions and KeyError fix...\n")
    
    tests = [
        ("Color Definitions", test_color_definitions),
        ("GUI Methods with Colors", test_gui_methods_with_colors),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
        print()
    
    print(f"{'='*70}")
    print(f"🧪 Color Fix Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All color fix tests passed!")
        print("\n✨ Color Issues Fixed:")
        print("   ✅ KeyError: 'warning' resolved")
        print("   ✅ KeyError: 'error' resolved")
        print("   ✅ All required colors defined")
        print("   ✅ Risk color coding works correctly")
        print("   ✅ Disclaimer frame displays properly")
        print("   ✅ GUI methods execute without errors")
        print("\n💡 The GUI should now display results without KeyError!")
        print("   Run 'python main.py' to test the full interface.")
    else:
        print("⚠️ Some color fix tests failed")
        print("Check the specific test failures above")
    
    print(f"{'='*70}")

if __name__ == "__main__":
    main()
